import request from '@/utils/request';

const server = '/system/alpha';

/**
 *
 * @param {持仓数据更新时间} params
 * @returns
 */

export function getDateList(params) {
	return request({
		url: server + '/DateList/',
		method: 'get',
		params
	});
}

// 获取风险特征、风险收益特征可选时间列表
export function filter_risk_future(params) {
	return request({
		url: server + '/filter_risk_future/',
		method: 'get',
		params
	});
}
// 获取对比类型
export function TypeMsg(params) {
	return request({
		url: '/compare/TypeMsg/',
		method: 'get',
		params
	});
}
// 获取对比类型
export function TypeMsg2(params) {
	return request({
		url: server + '/TypeMsg/',
		method: 'get',
		params
	});
}
// beta筛选器
export function AlphaFilterV2(data) {
	return request({
		url: server + '/AlphaFilterV2/',
		method: 'post',
		data
	});
}
// 获取主题&行业列表
export function alphamsg() {
	return request({
		url:  '/Tools/alphamsg/',
		method: 'get'
	});
}
export function getStyleList(params) {
	return requestSystem({
		url: server + '/filter/styleList/',
		method: 'get',
		params
	});
}
export function getTKandJYcategory(params) {
	return requestSystem({
		url: server + '/filter/TKandJYcategory/',
		method: 'get',
		params
	});
}
// 获得筛选结果
export function filterResult(data) {
	return request({
		url: server + '/ScoreCard/ScoreSelectFund/',
		method: 'post',
		data
	});
}
// 获得打分结果
export function SelectedScore(data) {
	return request({
		url: server + '/ScoreCard/SelectedScore/',
		method: 'post',
		data
	});
}
// 获得打分结果v2
export function SelectedScoreV2(data) {
	return request({
		url: server + '/ScoreCard/ScoreCardV2/',
		method: 'post',
		data
	});
}
// 获取动态回撤数据
export function getFofDrawdown(params) {
	return request({
		url: server + '/FofDrawdown/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {转债调性}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function cbondstylebenchmark(params) {
	return request({
		url: server + '/cbondstylebenchmark/',
		method: 'get',
		params
	});
}
// 获取基金/基金经理/基金公司收益曲线
export function getFundOrManagerReturn(data) {
	return request({
		url: server + '/FundOrManagerReturn/',
		method: 'post',
		data
	});
}
// 获取基准收益曲线
export function getIndexReturnInfo(data) {
	return request({
		url: server + '/IndexReturnInfo/',
		method: 'post',
		data
	});
}
// 获取基金/基金经理基础信息
export function getBasicInfo(params) {
	return request({
		url: server + '/ReturnInfo/',
		method: 'get',
		params
	});
}
// 收益率分布直方图
export function getFundReturnSection(data) {
	return request({
		url: server + '/ReturnSummary/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {风险收益指标基准列表} params
 *  fund_code: str(基金代码)
 *  type: str(基金类型)
 * @returns
 */

export function getBestBenchmarks(params) {
	return request({
		url: server + '/BestBenchmarks/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风险收益指标} params
 *  code: str(基金代码)
 *  type: str(基金类型)
 *  flag: str(基金/基金经理)
 * @returns
 */
export function getRiskFeatureYearly(params) {
	return request({
		url: server + '/RiskFeatureYearly/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风险收益关系} params
 * @returns
 */
export function getRiskFeatureRecent(params) {
	return request({
		url: server + '/RiskFeatureRecent/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {分时段业绩表现} params
 * lag=1&code=110022&type=equity&start_date&end_date&periodname=股票牛熊市场
 * @returns
 */
export function getMarketWindowReturn(params) {
	return request({
		url: server + '/MarketWindowReturn/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基准概念列表} params
 * @returns
 */
export function getFundPeriod(params) {
	return request({
		url: server + '/MacroPeriod/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {滚动胜率} params
 *  flag=1&code=110022&type=equity&start_date&end_date&benchmark=0
 * @returns
 */

export function getHoldPressureInfo(params) {
	return request({
		url: server + '/HoldPressureInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {滚动胜率基准列表} params
 *  code
 * @returns
 */
export function getAnalysisIndex(params) {
	return request({
		url: server + '/analysisindex/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {动态4因子统计}} params
 *  /FactorSummary/?flag=1&type=equity&code=000001&start_date=&end_date=&status=summary
 * @returns
 */
export function getDynamicStatistics(params) {
	return request({
		url: server + '/FactorSummary/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {TM模型分析}} params
 * @returns
 */
export function getTMStatistics(params) {
	return request({
		url: server + '/TMCapability/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {同类排名比较}} params
 * @returns
 */
export function getFofMeasureSinceRank(params) {
	return request({
		url: server + '/RiskFeatureYearly/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数列表} params
 * type
 * @returns
 */
export function getIndexList(params) {
	return request({
		url:   '/Tools/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {报告期持仓统计指数收益} params
 * code
 * @returns
 */

export function getIndexReturn(data) {
	return request({
		url: '/RateInfo/',
		method: 'post',
		data
	});
}

export function getAllocationDetails(params) {
	return request({
		url: server + '/AllocationDetails/',
		method: 'get',
		params
	});
}
export function getFofAllocationDetails(params) {
	return request({
		url: server + '/FofAllocationDetails/',
		method: 'get',
		params
	});
}
export function getFundMessage(params) {
	return request({
		url: server + '/fund_message/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {基金持仓分析} params
 * @returns
 * yearqtr fund_code
 */
export function getFofHoldFund(params) {
	return request({
		url: server + '/FofHoldFund/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {最新各类型基金配置情况}} params
 * @returns
 */
export function getFofAllocationMsg(params) {
	return request({
		url: server + '/FofAllocationMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金alpha/beta/smartbeta分解} params
 * @returns
 *  fund_code
 */
export function getFofAlphaRank(params) {
	return request({
		url: server + '/FofAlphaRank/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金标签分析}} params
 * @returns
 */
export function getFoFEquityTag(params) {
	return request({
		url: server + '/FoFEquityTag/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {权益基金股票分析}} params
 * @returns
 * fund_code
 */
export function getFoFHoldingNewest(params) {
	return request({
		url: server + '/HoldStocks/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {长期持有个股} params
 * /StocksLongHold/?flag=1&code=110022&type=equity
 * @returns
 */

export function getStocksLongHold(params) {
	return request({
		url: server + '/StocksLongHold/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {直投股票capm分析}} params
 * @returns
 */
export function getCapmBenchmark(params) {
	return request({
		url: server + '/CapmBenchmark/',
		method: 'get',
		params
	});
}
export function getCapmAnalysis(params) {
	return request({
		url: server + '/CapmAnalysis/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {股票风格特征}} params
 * /CreditDownRation/?flag=1&code=000045&type=bond
 * @returns
 */
export function getCharacteristicsBarra(params) {
	return request({
		url: server + '/BarraInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {产品分析} params
 * @returns
 * fund_code,type,index_code,start_date,end_date
 */
export function getFofImmediateAsset(params) {
	return request({
		url: server + '/FofImmediateAsset/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {产品分析-基准列表} params
 * @returns
 * type
 */
export function getIndexBasicMsg(params) {
	return request({
		url: server + '/BenchmarkList/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {短期流动性管理} params
 *  fund_code
 * @returns
 */
export function getFofLiquidity(params) {
	return request({
		url: server + '/FofLiquidity/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {行业配置表现} params
 *  { flag: 2, code: this.manager_code, type: 'activeequity', industry_section: '申万(2021)' }
 * @returns
 */

// 获取基金/基金经理 行业评价数据
export function getIndustryInfo(params) {
	return request({
		url: server + '/IndustryInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {持仓债券分析} params
 *
 * @returns
 */
export function getBondAnalysise(params) {
	return request({
		url: server + '/BondHoldingMsg/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {风格择时能力}} params
 * /TimmingStyle/?flag=1&type=bond&code=000045
 * @returns
 */
export function getStyleTiming(params) {
	return request({
		url: server + '/TimingStyle/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {What-if 假想验证：调仓时的资产配置作对了吗} params
 * @returns
 */
export function getWhatIf(params) {
	return request({
		url: server + '/ImaginaryNav/',
		method: 'get',
		params
	});
}
// 异常点
export function abnorlmalPoint(params) {
	return request({
		url: server + '/ReturnAbnormal/',
		method: 'get',
		params
	});
}
// 获取自定义情景列表
export function getSceneList(params) {
	return request({
		url: server + '/custom/page',
		method: 'get',
		params
	});
}

// 新增或修改自定义场景
export function postSceneList(data) {
	return request({
		url: server + '/custom/save',
		method: 'post',
		data
	});
}
// 获取情景分析数据
export function deleteCustomScene(params) {
	return request({
		url: server + '/custom/delete',
		method: 'post',
		params
	});
}

// 获取情景分析数据
export function getSceneChartData(data) {
	return request({
		url: server + '/custom/chartData',
		method: 'post',
		data
	});
}
/**
 *
 * @param {获取自定义显示模版列表} params
 * @returns
 */
export function getUserConfig(params) {
	return request({
		url: server + '/UserConfig/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {获取自定义模版详情} params
 * @returns
 */
export function getUserConfigInfo(params) {
	return request({
		url: server + '/UserConfigInfo/',
		method: 'get',
		params
	});
}
/**
 *
 * @param {新增自定义模版} params
 * @returns
 */
export function postUserConfigInfo(data) {
	return request({
		url: server + '/UserConfigInfo/',
		method: 'post',
		data
	});
}
/**
 *
 * @param {修改自定义模版} params
 * @returns
 */
export function putUserConfigInfo(data) {
	return request({
		url: server + '/UserConfigInfo/',
		method: 'put',
		data
	});
}
/**
 *
 * @param {删除自定义模版详} params
 * @returns
 */
export function deleteUserConfigInfo(params) {
	return request({
		url: server + '/UserConfigInfo/',
		method: 'delete',
		params
	});
}
// 获取基金 规模及持有人结构
export function getMoneyScale(params) {
	return request({
		url: server + '/money_scale/',
		method: 'get',
		params
	});
}
export function getFundHold(params) {
	return request({
		url: server + '/fund_hold/',
		method: 'get',
		params
	});
}
// 获取基金/基金经理 行业评价数据
export function getHoldStocks(params) {
	return request({
		url: server + '/HoldStocks/',
		method: 'get',
		params
	});
}

export function getSinceWhat(params) {
	return request({
		url:  '/Tools/since_what/',
		method: 'get',
		params
	});
}
export function getMeasureHint(params) {
	return request({
		url: server + '/MeasureHint',
		method: 'get',
		params
	});
}
// 获取分类 AlphaFilterTypes/
export function getTypeList(params) {
	return request({
		url: server + '/AlphaFilterTypes/',
		method: 'get',
		params
	});
}
