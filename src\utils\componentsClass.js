export let componentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'money'
		],
		templateList: [
			{
				name: '业绩评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluation',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '业绩表现',
				is: 'performanceYear',
				value: 'performanceYear',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond'
				],
				type: 'small_template',
				isshow: true
			},
			{
				name: '除固收外其他债券基金经理评价',
				is: 'evaluateManagers',
				value: 'evaluateManagers',
				typelist: ['cbond', 'bill', 'purebond', 'money'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '表现风格',
				is: 'performanceStyle',
				value: 'performanceStyle',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '操盘风格',
				is: 'holdingStyle',
				value: 'holdingStyle',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond'
					// 'bill'
				],
				type: 'small_template',
				isshow: true
			},
			{
				name: '近期公告风格',
				is: 'recentAnnouncementStyle',
				value: 'recentAnnouncementStyle',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '因子暴露与收益',
				is: 'barraReturnLine',
				value: 'barraReturnLine',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '年度风格',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
				is: 'debtBasedStyle',
				value: 'debtBasedStyle',
				type: 'small_template',
				isshow: true
			},
			{
				name: '近期风格',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
				is: 'recentStyle',
				value: 'recentStyle',
				type: 'small_template',
				isshow: true
			},
			{
				name: '固收+的加法描述',
				typelist: ['bond', 'obond'],
				is: 'additiveDescription',
				value: 'additiveDescription',
				type: 'small_template',
				isshow: true
			},
			{
				name: '市场风格表现',
				typelist: ['bond'],
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesis',
				type: 'small_template',
				isshow: true
			},
			{
				name: '行业能力圈',
				is: 'industryCapacity',
				value: 'industryCapacity',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '市场适应性',
				is: 'marketAdaptability',
				value: 'marketAdaptability',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '行业高低配',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
				is: 'industryEvaluation',
				value: 'industryEvaluation',
				type: 'big_template',
				isshow: true
			},
			{
				name: '规模及持有人结构',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond', 'money'],
				is: 'sizeStructure',
				value: 'sizeStructure',
				type: 'big_template',
				isshow: true
			}

			// CapabilityInfo接口异常，暂时搁置
			// {
			// 	name: '基金经理评价',
			// 	is: 'fundManagerEvaluation',
			// 	value: 'fundManagerEvaluation',
			// 	typelist: ['bond', 'obond'],
			// 	type: 'small_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '业绩与适应性分析',
		key: 'performance',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof',
			'money'
		],
		templateList: [
			{
				name: '总体业绩表现',
				is: 'allReturnPerformance',
				value: 'allReturnPerformance',
				type: 'big_template',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '风险收益指标',
				is: 'riskReturnIndex',
				value: 'riskReturnIndex',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			},
			/** 2025-05-29 删除 */
			// {
			// 	name: '风险收益关系',
			// 	is: 'riskReturnRelationship',
			// 	value: 'riskReturnRelationship',
			// 	typelist: [
			// 		'equity',
			// 		'equityhk',
			// 		'equitywithhk',
			// 		'equityhk-index',
			// 		'equityenhance',
			// 		'equityindex',
			// 		'bond',
			// 		'obond',
			// 		'cbond',
			// 		'bill',
			// 		'purebond',
			// 		'fof',
			// 		'money'
			// 	],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '滚动胜率',
				is: 'holdingPressure',
				value: 'holdingPressure',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			}
			/** 2025-05-29 删除 */
			// {
			// 	name: '市场适应性(带解释)',
			// 	is: 'marketAdaptabilityAndDescription',
			// 	value: 'marketAdaptabilityAndDescription',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	type: 'big_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '资产配置与风格',
		key: 'styleAnalysis',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof'
		],
		templateList: [
			{
				name: '基金资产配置分析',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '债券资产配置',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond', 'fof'],
				isshow: true
			},
			{
				name: '全持仓风格分析',
				is: 'positionStyle',
				value: 'positionStyle',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '当前权益持仓风格',
				is: 'equityStyle',
				value: 'equityStyle',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '权益分年度持仓风格',
				is: 'styleLabel',
				value: 'styleLabel',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '整体财务指标',
				is: 'financialIndex',
				value: 'financialIndex',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '报告期换手率',
				is: 'reportTurnover',
				value: 'reportTurnover',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '持股集中度',
				is: 'holdStockConcentration',
				value: 'holdStockConcentration',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '基金持仓分析',
				is: 'fundPositionAnalysis',
				value: 'fundPositionAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			},
			{
				name: '最新各类型基金配置情况',
				is: 'newFundAllocation',
				value: 'newFundAllocation',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			},
			{
				name: '短期流动性管理',
				is: 'mobilityManage',
				value: 'mobilityManage',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			}
		]
	},
	{
		label: '行业分析',
		key: 'equityAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
		templateList: [
			{
				name: '已披露行业配置',
				is: 'industryReportPosition',
				value: 'industryReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '行业配置变化半年度堆叠图',
				is: 'industryPositionChange',
				value: 'industryPositionChange',
				type: 'big_template',
				isshow: true,
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				isshow: true
			},
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			}
			/** 2025-05-29 删除 */
			// {
			// 	name: '行业高低配',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	is: 'industryEvaluation',
			// 	value: 'industryEvaluation',
			// 	type: 'big_template',
			// 	isshow: true
			// },
			// {
			// 	name: '行业能力圈',
			// 	is: 'industryCapacity',
			// 	value: 'industryCapacity',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	type: 'big_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '持股分析',
		key: 'abilityAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
		templateList: [
			{
				name: '股票PLUS',
				is: 'stockReturnPlus',
				value: 'stockReturnPlus',
				type: 'big_template',
				typelist: ['bond', 'obond'],
				isshow: true
			},
			{
				name: '已披露股票配置',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '长期持有个股',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				type: 'big_template',
				isshow: true
			},
			// {
			// 	name: '估值分析',
			// 	is: 'valuationAnalysis',
			// 	value: 'valuationAnalysis',
			// 	typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				typelist: [
					'equity',
					'neutral',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '持股加权估值水平',
				is: 'shareholdingWeightedValuation',
				value: 'shareholdingWeightedValuation',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '持仓加权盈利能力',
				is: 'positionWeightedProfitability',
				value: 'positionWeightedProfitability',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '前十大相对质量',
				is: 'topTenAttacks',
				value: 'topTenAttacks',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '六种买入卖出模式',
				is: 'sixBuyingSellModes',
				value: 'sixBuyingSellModes',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			}
		]
	},
	{
		label: '债券分析',
		key: 'bondAnalysis',
		class: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
		templateList: [
			{
				name: '持仓债券分析',
				is: 'bondReportPosition',
				value: 'bondReportPosition',
				type: 'big_template',
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
				isshow: true
			},
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			},
			{
				name: '信用挖掘',
				is: 'creditMining',
				value: 'creditMining',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			},
			{
				name: '基金报告期披露信用债评级分布(近年)',
				is: 'creditBondRatingDistribution',
				value: 'creditBondRatingDistribution',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			}
		]
	},
	{
		label: '转债分析',
		key: 'cbondAnalysis',
		class: ['bond', 'obond', 'cbond'],
		templateList: [
			{
				name: '转债季度信息',
				is: 'bondQuarterInfo',
				value: 'bondQuarterInfo',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond'],
				isshow: true
			},
			// {
			// 	name: '转债PLUS',
			// 	is: 'cbondReturnPlus',
			// 	value: 'cbondReturnPlus',
			// 	type: 'big_template',
			// 	typelist: ['bond', 'obond', 'cbond'],
			// 	isshow: true
			// },
			{
				name: '转债正股风格',
				is: 'cbondStyle',
				value: 'cbondStyle',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond'],
				isshow: true
			},
			{
				name: '转债股性债性概览',
				is: 'cbondHoldEquityBond',
				value: 'cbondHoldEquityBond',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond'],
				isshow: true
			}
		]
	},
	{
		label: '归因分析',
		key: 'attributionAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
		templateList: [
			{
				name: '基金利润分析',
				is: 'fundProfitAnalysis',
				value: 'fundProfitAnalysis',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '利息收益分析',
				is: 'interestIncomeAnalysis',
				value: 'interestIncomeAnalysis',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '投资收益分析',
				is: 'investmentIncomeAnalysis',
				value: 'investmentIncomeAnalysis',
				typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
				type: 'big_template',
				isshow: true
			},
			{
				name: 'Brinson归因',
				is: 'brinsonAttribution',
				value: 'brinsonAttribution',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			// {
			// 	name: 'TM模型分析',
			// 	is: 'TMModelAnalysis',
			// 	value: 'TMModelAnalysis',
			// 	typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: 'PB-ROE分析',
				is: 'PBROEcharacteristics',
				value: 'PBROEcharacteristics',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			{
				name: '调仓效果',
				is: 'warehouseAdjustmentRhythm',
				value: 'warehouseAdjustmentRhythm',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			{
				name: '分年度类Brinson归因',
				is: 'excessReturnAttribution',
				value: 'excessReturnAttribution',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			}
		]
	},
	{
		label: 'FOF持仓PLUS',
		key: 'fofAnalysis',
		class: ['fof'],
		templateList: [
			{
				name: 'FOF基金分析',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			}
		]
	},
	{
		label: '怎么挣钱',
		key: 'moneyAnalysis',
		class: ['money'],
		templateList: [
			{
				name: '基金报告期披露信用债评级分布(近年)',
				is: 'creditBondRatingDistribution',
				value: 'creditBondRatingDistribution',
				type: 'big_template',
				isshow: true,
				typelist: ['money'],
				isshow: true
			},
			{
				name: '基金杠杆率',
				is: 'leverageLevel',
				value: 'leverageLevel',
				type: 'big_template',
				isshow: true,
				typelist: ['money'],
				isshow: true
			}
			/**
			 * 数据没有更新
			 */
			// {
			// 	name: '基金平均剩余期限',
			// 	is: 'averageRemainingMaturity',
			// 	value: 'averageRemainingMaturity',
			// 	type: 'big_template',
			// 	isshow: true,
			// 	typelist: ['money'],
			// 	isshow: true
			// },
			// {
			// 	name: '基金非交易日变动',
			// 	is: 'noTradingDayChanges',
			// 	value: 'noTradingDayChanges',
			// 	type: 'big_template',
			// 	isshow: true,
			// 	typelist: ['money'],
			// 	isshow: true
			// }
		]
	}
];
export let componentsManagerList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'money'
		],
		templateList: [
			{
				name: '业绩评价',
				is: 'performanceCapabilityEvaluation',
				value: 'performanceCapabilityEvaluation',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '业绩表现',
				is: 'performanceYear',
				value: 'performanceYear',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond'
				],
				type: 'small_template',
				isshow: true
			},
			{
				name: '除固收外其他债券基金经理评价',
				is: 'evaluateManagers',
				value: 'evaluateManagers',
				typelist: ['cbond', 'bill', 'purebond', 'money'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '表现风格',
				is: 'performanceStyle',
				value: 'performanceStyle',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '操盘风格',
				is: 'holdingStyle',
				value: 'holdingStyle',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond'
					// 'bill'
				],
				type: 'small_template',
				isshow: true
			},
			{
				name: '近期公告风格',
				is: 'recentAnnouncementStyle',
				value: 'recentAnnouncementStyle',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '因子暴露与收益',
				is: 'barraReturnLine',
				value: 'barraReturnLine',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true
			},
			// {
			// 	name: '年度风格',
			// 	typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	is: 'debtBasedStyle',
			// 	value: 'debtBasedStyle',
			// 	type: 'small_template',
			// 	isshow: true
			// },
			// {
			// 	name: '近期风格',
			// 	typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	is: 'recentStyle',
			// 	value: 'recentStyle',
			// 	type: 'small_template',
			// 	isshow: true
			// },
			// {
			// 	name: '固收+的加法描述',
			// 	typelist: ['bond', 'obond'],
			// 	is: 'additiveDescription',
			// 	value: 'additiveDescription',
			// 	type: 'small_template',
			// 	isshow: true
			// },
			{
				name: '市场风格表现',
				typelist: ['bond'],
				is: 'stockDebtSynthesis',
				value: 'stockDebtSynthesis',
				type: 'small_template',
				isshow: true
			},
			{
				name: '行业能力圈',
				is: 'industryCapacity',
				value: 'industryCapacity',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '市场适应性',
				is: 'marketAdaptability',
				value: 'marketAdaptability',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				type: 'small_template',
				isshow: true
			},
			{
				name: '行业高低配',
				typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk'],
				is: 'industryEvaluation',
				value: 'industryEvaluation',
				type: 'big_template',
				isshow: true
			},
			// {
			// 	name: '规模及持有人结构',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond', 'money'],
			// 	is: 'sizeStructure',
			// 	value: 'sizeStructure',
			// 	type: 'big_template',
			// 	isshow: true
			// }

			// CapabilityInfo接口异常，暂时搁置
			// {
			// 	name: '基金经理评价',
			// 	is: 'fundManagerEvaluation',
			// 	value: 'fundManagerEvaluation',
			// 	typelist: ['bond', 'obond'],
			// 	type: 'small_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '业绩与适应性分析',
		key: 'performance',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof',
			'money'
		],
		templateList: [
			{
				name: '总体业绩表现',
				is: 'allReturnPerformance',
				value: 'allReturnPerformance',
				type: 'big_template',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '收益率分布直方图',
				is: 'distributionReturn',
				value: 'distributionReturn',
				type: 'big_template',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '风险收益指标',
				is: 'riskReturnIndex',
				value: 'riskReturnIndex',
				typelist: [
					'equity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			},
			/** 2025-05-29 删除 */
			// {
			// 	name: '风险收益关系',
			// 	is: 'riskReturnRelationship',
			// 	value: 'riskReturnRelationship',
			// 	typelist: [
			// 		'equity',
			// 		'equityhk',
			// 		'equitywithhk',
			// 		'equityhk-index',
			// 		'equityenhance',
			// 		'equityindex',
			// 		'bond',
			// 		'obond',
			// 		'cbond',
			// 		'bill',
			// 		'purebond',
			// 		'fof',
			// 		'money'
			// 	],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: '分时段业绩表现',
				is: 'timePhasedPerformance',
				value: 'timePhasedPerformance',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '滚动胜率',
				is: 'holdingPressure',
				value: 'holdingPressure',
				typelist: [
					'equity',
					'activeequity',
					'equityhk',
					'equitywithhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				type: 'big_template',
				isshow: true
			}
			/** 2025-05-29 删除 */
			// {
			// 	name: '市场适应性(带解释)',
			// 	is: 'marketAdaptabilityAndDescription',
			// 	value: 'marketAdaptabilityAndDescription',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	type: 'big_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '资产配置与风格',
		key: 'styleAnalysis',
		class: [
			'equity',
			'activeequity',
			'equitywithhk',
			'equityhk',
			'equityhk-index',
			'equityenhance',
			'equityindex',
			'bond',
			'obond',
			'cbond',
			'bill',
			'purebond',
			'fof'
		],
		templateList: [
			{
				name: '基金资产配置分析',
				is: 'fundAssetAllocationAnalysis',
				value: 'fundAssetAllocationAnalysis',
				type: 'big_template',
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'cbond',
					'bill',
					'purebond',
					'fof'
				],
				isshow: true
			},
			{
				name: '债券资产配置',
				is: 'positionClass',
				value: 'positionClass',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond', 'fof'],
				isshow: true
			},
			{
				name: '全持仓风格分析',
				is: 'positionStyle',
				value: 'positionStyle',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '当前权益持仓风格',
				is: 'equityStyle',
				value: 'equityStyle',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '权益分年度持仓风格',
				is: 'styleLabel',
				value: 'styleLabel',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '整体财务指标',
				is: 'financialIndex',
				value: 'financialIndex',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '报告期换手率',
				is: 'reportTurnover',
				value: 'reportTurnover',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '持股集中度',
				is: 'holdStockConcentration',
				value: 'holdStockConcentration',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex'],
				isshow: true
			},
			{
				name: '基金持仓分析',
				is: 'fundPositionAnalysis',
				value: 'fundPositionAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			},
			{
				name: '最新各类型基金配置情况',
				is: 'newFundAllocation',
				value: 'newFundAllocation',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			},
			{
				name: '短期流动性管理',
				is: 'mobilityManage',
				value: 'mobilityManage',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			}
		]
	},
	{
		label: '行业分析',
		key: 'equityAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
		templateList: [
			{
				name: '已披露行业配置',
				is: 'industryReportPosition',
				value: 'industryReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '行业配置变化半年度堆叠图',
				is: 'industryPositionChange',
				value: 'industryPositionChange',
				type: 'big_template',
				isshow: true,
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				isshow: true
			},
			{
				name: '行业配置表现',
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			}
			/** 2025-05-29 删除 */
			// {
			// 	name: '行业高低配',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	is: 'industryEvaluation',
			// 	value: 'industryEvaluation',
			// 	type: 'big_template',
			// 	isshow: true
			// },
			// {
			// 	name: '行业能力圈',
			// 	is: 'industryCapacity',
			// 	value: 'industryCapacity',
			// 	typelist: ['equity', 'activeequity', 'equityhk', 'equitywithhk', 'bond', 'obond'],
			// 	type: 'big_template',
			// 	isshow: true
			// }
		]
	},
	{
		label: '持股分析',
		key: 'abilityAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
		templateList: [
			// {
			// 	name: '股票PLUS',
			// 	is: 'stockReturnPlus',
			// 	value: 'stockReturnPlus',
			// 	type: 'big_template',
			// 	typelist: ['bond', 'obond'],
			// 	isshow: true
			// },
			{
				name: '已披露股票配置',
				is: 'stocksReportPosition',
				value: 'stocksReportPosition',
				type: 'big_template',
				isshow: true,
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '长期持有个股',
				is: 'longTermHoldingShares',
				value: 'longTermHoldingShares',
				typelist: [
					'equity',
					'activeequity',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				type: 'big_template',
				isshow: true
			},
			// {
			// 	name: '估值分析',
			// 	is: 'valuationAnalysis',
			// 	value: 'valuationAnalysis',
			// 	typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: 'PB-ROE估值',
				is: 'PBROEvaluation',
				value: 'PBROEvaluation',
				typelist: [
					'equity',
					'neutral',
					'equitywithhk',
					'equityhk',
					'equityhk-index',
					'equityenhance',
					'equityindex',
					'bond',
					'obond',
					'bill'
				],
				type: 'big_template',
				isshow: true
			},
			{
				name: '持股加权估值水平',
				is: 'shareholdingWeightedValuation',
				value: 'shareholdingWeightedValuation',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '持仓加权盈利能力',
				is: 'positionWeightedProfitability',
				value: 'positionWeightedProfitability',
				typelist: ['equity', 'equitywithhk', 'equityhk', 'equityhk-index', 'equityenhance', 'equityindex', 'bond', 'obond', 'bill'],
				type: 'big_template',
				isshow: true
			},
			{
				name: '前十大相对质量',
				is: 'topTenAttacks',
				value: 'topTenAttacks',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			},
			{
				name: '六种买入卖出模式',
				is: 'sixBuyingSellModes',
				value: 'sixBuyingSellModes',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk', 'bond', 'obond'],
				isshow: true
			}
		]
	},
	{
		label: '债券分析',
		key: 'bondAnalysis',
		class: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
		templateList: [
			// {
			// 	name: '持仓债券分析',
			// 	is: 'bondReportPosition',
			// 	value: 'bondReportPosition',
			// 	type: 'big_template',
			// 	typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond', 'fof'],
			// 	isshow: true
			// },
			{
				name: '久期拉长',
				is: 'prolongedDuration',
				value: 'prolongedDuration',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			},
			{
				name: '信用挖掘',
				is: 'creditMining',
				value: 'creditMining',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			},
			{
				name: '基金报告期披露信用债评级分布(近年)',
				is: 'creditBondRatingDistribution',
				value: 'creditBondRatingDistribution',
				type: 'big_template',
				isshow: true,
				typelist: ['bill', 'bond', 'obond', 'cbond', 'purebond'],
				isshow: true
			}
		]
	},
	{
		label: '转债分析',
		key: 'cbondAnalysis',
		class: ['bond', 'obond', 'cbond'],
		templateList: [
			{
				name: '转债季度信息',
				is: 'bondQuarterInfo',
				value: 'bondQuarterInfo',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond'],
				isshow: true
			},
			// {
			// 	name: '转债PLUS',
			// 	is: 'cbondReturnPlus',
			// 	value: 'cbondReturnPlus',
			// 	type: 'big_template',
			// 	typelist: ['bond', 'obond', 'cbond'],
			// 	isshow: true
			// },
			// {
			// 	name: '转债正股风格',
			// 	is: 'cbondStyle',
			// 	value: 'cbondStyle',
			// 	type: 'big_template',
			// 	typelist: ['bond', 'obond', 'cbond'],
			// 	isshow: true
			// },
			{
				name: '转债股性债性概览',
				is: 'cbondHoldEquityBond',
				value: 'cbondHoldEquityBond',
				type: 'big_template',
				typelist: ['bond', 'obond', 'cbond'],
				isshow: true
			}
		]
	},
	{
		label: '归因分析',
		key: 'attributionAnalysis',
		class: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
		templateList: [
			// {
			// 	name: '基金利润分析',
			// 	is: 'fundProfitAnalysis',
			// 	value: 'fundProfitAnalysis',
			// 	typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			// {
			// 	name: '利息收益分析',
			// 	is: 'interestIncomeAnalysis',
			// 	value: 'interestIncomeAnalysis',
			// 	typelist: ['bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			// {
			// 	name: '投资收益分析',
			// 	is: 'investmentIncomeAnalysis',
			// 	value: 'investmentIncomeAnalysis',
			// 	typelist: ['equity', 'equityhk', 'equitywithhk', 'bond', 'obond', 'cbond', 'bill', 'purebond'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: 'Brinson归因',
				is: 'brinsonAttribution',
				value: 'brinsonAttribution',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			// {
			// 	name: 'TM模型分析',
			// 	is: 'TMModelAnalysis',
			// 	value: 'TMModelAnalysis',
			// 	typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
			// 	type: 'big_template',
			// 	isshow: true
			// },
			{
				name: 'PB-ROE分析',
				is: 'PBROEcharacteristics',
				value: 'PBROEcharacteristics',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			{
				name: '调仓效果',
				is: 'warehouseAdjustmentRhythm',
				value: 'warehouseAdjustmentRhythm',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			},
			{
				name: '分年度类Brinson归因',
				is: 'excessReturnAttribution',
				value: 'excessReturnAttribution',
				type: 'big_template',
				typelist: ['equity', 'activeequity', 'equitywithhk', 'equityhk'],
				isshow: true
			}
		]
	},
	{
		label: 'FOF持仓PLUS',
		key: 'fofAnalysis',
		class: ['fof'],
		templateList: [
			{
				name: 'FOF基金分析',
				is: 'fundTypeAnalysis',
				value: 'fundTypeAnalysis',
				type: 'big_template',
				typelist: ['fof'],
				isshow: true
			}
		]
	},
	{
		label: '怎么挣钱',
		key: 'moneyAnalysis',
		class: ['money'],
		templateList: [
			{
				name: '基金报告期披露信用债评级分布(近年)',
				is: 'creditBondRatingDistribution',
				value: 'creditBondRatingDistribution',
				type: 'big_template',
				isshow: true,
				typelist: ['money'],
				isshow: true
			},
			{
				name: '基金杠杆率',
				is: 'leverageLevel',
				value: 'leverageLevel',
				type: 'big_template',
				isshow: true,
				typelist: ['money'],
				isshow: true
			}
			/**
			 * 数据没有更新
			 */
			// {
			// 	name: '基金平均剩余期限',
			// 	is: 'averageRemainingMaturity',
			// 	value: 'averageRemainingMaturity',
			// 	type: 'big_template',
			// 	isshow: true,
			// 	typelist: ['money'],
			// 	isshow: true
			// },
			// {
			// 	name: '基金非交易日变动',
			// 	is: 'noTradingDayChanges',
			// 	value: 'noTradingDayChanges',
			// 	type: 'big_template',
			// 	isshow: true,
			// 	typelist: ['money'],
			// 	isshow: true
			// }
		]
	}
];
export let companyComponentsList = [
	{
		label: '一页通',
		key: 'onePagePass',
		class: ['*'],
		templateList: [
			{
				name: '所属基金概况',
				is: 'allTypeFundBasicInfo',
				value: 'allTypeFundBasicInfo',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllTypeFundBaicInfoData',
				getRequestData: 'getAllTypeFundBaicInfo'
			},
			{
				name: '各类型基金业绩',
				is: 'allTypeFundCumReturn',
				value: 'allTypeFundCumReturn',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllTypeFundCumReturnData',
				getRequestData: 'getAllTypeFundCumReturn'
			},
			{
				name: '基金公司规模',
				is: 'fundCompanyNetasset',
				value: 'fundCompanyNetasset',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getFundCompanyNetassetData',
				getRequestData: 'getFundCompanyNetassetList'
			},
			{
				name: '最新基金业绩排名',
				is: 'performanceRanking',
				value: 'performanceRanking',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getPerformanceRankingData',
				getRequestData: 'getPerformanceRankingList'
			},
			{
				name: '基金资产配置分析',
				is: 'allocationAnalysis',
				value: 'allocationAnalysis',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getCompanyAllocationDetailsData',
				getRequestData: 'getCompanyAllocationDetails'
			},
			// {
			// 	name: '全部基金',
			// 	is: 'allFunds',
			// 	value: 'allFunds',
			// 	type: 'big_template',
			// 	typelist: ['*'],
			// 	isshow: true,
			// 	getData: 'getAllFundsData',
			// 	getRequestData: 'getAllFunds'
			// },
			{
				name: '全部基金经理',
				is: 'allFundManagers',
				value: 'allFundManagers',
				type: 'big_template',
				typelist: ['*'],
				isshow: true,
				getData: 'getAllFundManagersData',
				getRequestData: 'getHoldType'
			}
		]
	},
	{
		label: '主动权益',
		key: 'equity',
		class: ['equity'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['equity'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['equity'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			}
		]
	},
	{
		label: '港股',
		key: 'equityhk',
		class: ['equityhk'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['equityhk'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['equityhk'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			}
		]
	},
	{
		label: '固收+',
		key: 'bond',
		class: ['bond'],
		templateList: [
			{
				name: '持仓股票分析',
				is: 'positionStockAnalysis',
				value: 'positionStockAnalysis',
				type: 'big_template',
				typelist: ['bond'],
				isshow: true,
				getData: 'getHoldStockMsgData',
				getRequestData: 'getHoldStockMsg'
			},
			{
				name: '行业配置表现',
				typelist: ['bond'],
				is: 'industryAllocationPerformance',
				value: 'industryAllocationPerformance',
				type: 'big_template',
				isshow: true,
				getData: 'getIndustryInfoData',
				getRequestData: 'getIndustryInfo'
			},
			{
				name: '持仓债券分析',
				is: 'positionBondAnalysis',
				typelist: ['bond'],
				value: 'positionBondAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondAnalysisData',
				getRequestData: 'getBondAnalysis'
			},
			{
				name: '信用分析',
				is: 'creditAnalysis',
				typelist: ['bond'],
				value: 'creditAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondCreditAnalysisData',
				getRequestData: 'getBondCreditAnalysis'
			}
		]
	},
	{
		label: '纯债',
		key: 'purebond',
		class: ['purebond'],
		templateList: [
			{
				name: '持仓债券分析',
				is: 'positionBondAnalysis',
				typelist: ['purebond'],
				value: 'positionBondAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondAnalysisData',
				getRequestData: 'getBondAnalysis'
			},
			{
				name: '信用分析',
				is: 'creditAnalysis',
				typelist: ['purebond'],
				value: 'creditAnalysis',
				type: 'big_template',
				isshow: true,
				getData: 'getBondCreditAnalysisData',
				getRequestData: 'getBondCreditAnalysis'
			}
		]
	}
	// {
	// 	label: '资产分析',
	// 	key: 'assetAnalysis',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '持仓股票分析',
	// 			is: 'positionStockAnalysis',
	// 			value: 'positionStockAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['equity'],
	// 			isshow: true,
	// 			methods: 'getHoldStockMsg',
	// 			getData: 'getHoldStockMsgData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		{
	// 			name: '行业配置表现',
	// 			typelist: ['equity'],
	// 			is: 'industryAllocationPerformance',
	// 			value: 'industryAllocationPerformance',
	// 			type: 'big_template',
	// 			isshow: true,
	// 			getData: 'getIndustryInfoData',
	// 			getRequestData: 'getIndustryInfo'
	// 		},
	// 		{
	// 			name: '配置变化对比',
	// 			is: 'configurationChangeComparison',
	// 			value: 'configurationChangeComparison',
	// 			type: 'big_template',
	// 			typelist: ['equity'],
	// 			isshow: true,
	// 			methods: 'getIndustryChange',
	// 			getData: 'getIndustryChangeData',
	// 			getRequestData: 'getDateList'
	// 		},
	// 		{
	// 			name: '持仓债券分析',
	// 			is: 'positionBondAnalysis',
	// 			value: 'positionBondAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['bond', 'purebond'],
	// 			isshow: true,
	// 			methods: 'getPositionBondAnalysis',
	// 			getData: 'getPositionBondAnalysisData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		{
	// 			name: '信用分析',
	// 			is: 'creditAnalysis',
	// 			value: 'creditAnalysis',
	// 			type: 'big_template',
	// 			typelist: ['bond', 'purebond'],
	// 			isshow: true,
	// 			methods: 'getCreditAnalysis',
	// 			getData: 'getCreditAnalysisData',
	// 			getRequestData: 'getTypeList'
	// 		},
	// 		// {
	// 		// 	name: '评价指标',
	// 		// 	is: 'GDBankHf',
	// 		// 	value: 'GDBankHf',
	// 		// 	type: 'big_template',
	// 		// 	typelist: ['hf_equity'],
	// 		// 	isshow: true,
	// 		// 	getData: 'getGDBankHfDetailData',
	// 		// 	getRequestData: undefined
	// 		// },
	// 		{
	// 			name: '产品类型饼状图',
	// 			is: 'fundPieChart',
	// 			value: 'fundPieChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getFundPieChartData',
	// 			getRequestData: 'getFundPieChart'
	// 		},
	// 		{
	// 			name: '公司规模及人员变动图',
	// 			is: 'companySizeChange',
	// 			value: 'companySizeChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getCompanySizeChangeData',
	// 			getRequestData: 'getCompanySizeChange'
	// 		},
	// 		{
	// 			name: '存续期间变化',
	// 			is: 'durationChange',
	// 			value: 'durationChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getDurationChangeData',
	// 			getRequestData: 'getDurationChange'
	// 		},
	// 		{
	// 			name: '收益图',
	// 			is: 'incomeChart',
	// 			value: 'incomeChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getIncomeChartData',
	// 			getRequestData: 'getIncomeChart'
	// 		},
	// 		{
	// 			name: '回撤图',
	// 			is: 'retracementChart',
	// 			value: 'retracementChart',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getRetracementChartData',
	// 			getRequestData: 'getRetracementChart'
	// 		},
	// 		{
	// 			name: '高管变动',
	// 			is: 'managerChange',
	// 			value: 'managerChange',
	// 			type: 'big_template',
	// 			typelist: ['hf_equity', 'hf_bond'],
	// 			isshow: true,
	// 			getData: 'getManagerChangeData',
	// 			getRequestData: 'getManagerChange'
	// 		}
	// 	]
	// },
	// {
	// 	label: '所属基金',
	// 	key: 'itsFund',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '新发基金',
	// 			is: 'newDevelopmentFund',
	// 			value: 'newDevelopmentFund',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			getData: 'getNewDevelopmentFundData',
	// 			getRequestData: 'getNewDevelopmentFund'
	// 		},
	// 		{
	// 			name: '全部基金',
	// 			is: 'allFunds',
	// 			value: 'allFunds',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			getData: 'getAllFundsData',
	// 			getRequestData: 'getAllFunds'
	// 		}
	// 	]
	// },
	// {
	// 	label: '所属基金经理',
	// 	key: 'itsFundManager',
	// 	class: ['*'],
	// 	templateList: [
	// 		{
	// 			name: '全部基金经理',
	// 			is: 'allFundManagers',
	// 			value: 'allFundManagers',
	// 			type: 'big_template',
	// 			typelist: ['*'],
	// 			isshow: true,
	// 			methods: 'getAllFundManagers',
	// 			getData: 'getAllFundManagersData',
	// 			getRequestData: 'getHoldType'
	// 		}
	// 	]
	// }
];
