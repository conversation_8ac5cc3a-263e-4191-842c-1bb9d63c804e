<template>
	<div :id="'marketAdaptability' + (showDescription ? '' : 'Description')" style="position: relative">
		<analysis-card-title title="市场适应能力" :image_id="'marketAdaptability' + (showDescription ? '' : 'Description')">
			<!-- <QuickTimePicker v-model="preset_time" @change="changeTimePicker"></QuickTimePicker> -->
		</analysis-card-title>
		<div class="charts_center_class" v-loading="loading" :id="'industryCapacityMain' + (showDescription ? '' : 'Description')">
			<v-chart
				:ref="'industryCapacity' + (showDescription ? '' : 'Description')"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				style="width: 100%; height: 300px"
				:options="hangyeop"
			/>
		</div>
		<div v-if="showDescription">
			<analysis-description title="市场适应能力" :description="description"></analysis-description>
		</div>
		<!--<div class="flex_center" style="width: 100%" :id="'industryCapacityMain' + (showDescription ? '' : 'Description')">
			<div id="industryCapacity" v-loading="loading">
				<div class="charts_industry_class mr-40">
					<v-chart
						:ref="'industryCapacity' + (showDescription ? '' : 'Description')"
						autoresize
						v-loading="loading"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						style="width: 100%; height: 300px"
						:options="hangyeop"
					/>
				</div>
				 <div v-show="classShow" class="industry_capacity">
					<div
						v-for="item in industryClassList"
						:key="item.value"
						@click="changeSwitch(item)"
						style="display: flex; align-items: center; cursor: pointer; margin: 6px 2px; width: 100px"
					>
						<div
							:style="
								item.show
									? `width: 16px;border-Radius:2px; height: 8px;background:${item.color}`
									: `width: 16px; height: 8px;border-Radius:2px;background:#cccccc`
							"
						></div>
						<div style="margin-left: 8px; font-size: 12px; color: #54657e">
							{{ item.label }}
						</div>
					</div>
				</div> 
			</div>
			<div style="width: 602px" v-if="showDescription">
				<analysis-description title="市场适应能力" :description="description"></analysis-description>
			</div>
		</div>-->
	</div>
</template>

<script>
// 模型描述
import QuickTimePicker from '@/pages/tkdesign/marketAnalysis/component/QuickTimePicker.vue';
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
// 行业能力圈&&市场适应性
import { getFundMarketCapability } from '@/api/pages/Analysis.js';
import { barChartOption } from '@/utils/chartStyle.js';
export default {
	name: 'marketAdaptability',
	components: { QuickTimePicker, analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		description() {
			return `首先计算基金经理滚动表现，根据划分的各个不同阶段的市场阶段，统计基金经理在该阶段的收益表现以及 sharpe 等，并时间衰减求均值，就得到了在该市场状态下的表现值。对基金经理进行横向比较，得到该基金经理的分位数。`;
		}
	},
	data() {
		return {
			loading: true,
			show: true,
			preset_time: {
				radioValue: '1'
			},
			marketClassify: {
				money: ['松货币', '紧货币', '松信用', '紧信用'],
				bull: ['股牛债牛', '股牛债平', '股牛债熊', '股平债牛', '股平债平', '股平债熊', '股熊债牛', '股熊债平', '股熊债熊'],
				style: ['成长强势', '价值强势', '大盘强势', '小盘强势'],
				secondary: ['上升', '牛市', '震荡', '熊市', '反弹', '大跌'],
				inventory: ['被动去库存', '主动补库存', '被动补库存', '主动去库存'],
				macro: ['复苏', '繁荣', '滞涨', '衰退']
			},
			classifyTranseName: {
				money: '货币周期',
				bull: '牛熊市场周期',
				style: '市场风格',
				secondary: '二级市场周期',
				inventory: '库存周期',
				macro: '宏观周期'
			},
			industryBigList: {
				货币周期: ['松货币', '紧货币', '松信用', '紧信用'],
				牛熊市场周期: ['股牛债牛', '股牛债平', '股牛债熊', '股平债牛', '股平债平', '股平债熊', '股熊债牛', '股熊债平', '股熊债熊'],
				市场风格: ['成长强势', '价值强势', '大盘强势', '小盘强势'],
				二级市场周期: ['上升', '牛市', '震荡', '熊市', '反弹', '大跌'],
				库存周期: ['被动去库存', '主动补库存', '被动补库存', '主动去库存'],
				宏观周期: ['复苏', '繁荣', '滞涨', '衰退']
			},
			colorList: {
				money: '#B1B0F9',
				bull: '#FFD600',
				style: '#E8684A',
				secondary: '#7388A9',
				inventory: '#5AD8A6',
				macro: '#88C9E9'
			},
			hangyeop: {},
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1N3411F7VT?share_source=copy_web');
		},
		// 监听时间选择框改变
		changeTimePicker(val) {
			if (val?.radioValue) {
				if (val.radioValue == 'custom') {
					this.info.start_date = val.startDate;
					this.info.end_date = val.endDate;
				} else {
					this.info.start_date = this.moment().subtract(val.radioValue, 'years').format('YYYY-MM-DD');
					this.info.end_date = this.moment().format('YYYY-MM-DD');
				}
			}
			this.getFundMarketCapability();
		},
		// 获取数据
		async getData(info) {
			this.info = {
				...info,
				start_date: this.moment().subtract(this.preset_time.radioValue, 'years').format('YYYY-MM-DD'),
				end_date: this.moment().format('YYYY-MM-DD')
			};
			await this.getFundMarketCapability();
		},
		// 获取市场适应性
		async getFundMarketCapability() {
			this.loading = true;
			let data = await getFundMarketCapability({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag,
				name: this.info.name,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				periodname: '',
				description: ''
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				// this.getMarketData(data?.data);
				this.updateChart(data?.data);
			} else {
				// this.getMarketData([]);
				this.updateChart([]);
			}
		},
		// 图表样式修改 v2.0
		updateChart(data) {
			this.show = true;
			this.loading = false;
			let series = data.sort((a, b) => b.windowRank - a.windowRank);
			series = series.map((item) => {
				let industryClass = '其他';
				for (const key in this.industryBigList) {
					if (this.industryBigList[key].includes(item.description)) {
						industryClass = key;
					}
				}
				return {
					...item,
					industryClass
				};
			});
			let industrySection = Array.from(new Set(series.map((item) => item.industryClass)));
			this.hangyeop = barChartOption({
				toolbox: 'none',
				// color: ['#4096ff', '#FFD600', '#7388A9', '#E8684A', '#5AD8A6', '#88C9E9'],
				legend: {
					bottom: '0',
					data: industrySection
				},
				grid: {
					top: '12px',
					bottom: '38px'
				},
				tooltip: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					formatter(params) {
						let index = params.findIndex((v) => v.value != '-');
						let value = `
						<div style="font-weight:500;font-size:18pxmargin-bottom:8px">${params?.[index].seriesName}</div>
                        <div style="display:flex;justify-content:space-between;align-items:center;">
                            <div style="display:flex;align-items:center;">
                                <div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${
																	params?.[index].color
																};"></div>
                                <div>${params[index].name}</div>
                                <div style="margin-left:4px">:</div>
                            </div>
                            <div>${(params[index]?.value * 100).toFixed(2)}%</div>
                        </div>`;
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				xAxis: [
					{
						data: series.map((item) => {
							return item.description;
						}),
						isAlign: true
						// rotate: -45
					}
				],
				series: industrySection.map((item) => {
					return {
						name: item,
						stack: 'Total',
						type: 'bar',
						data: series.map((v) => (v.industryClass == item ? v.windowRank : '-'))
					};
				})
			});
		},
		// 获取数据
		getMarketData(data) {
			this.show = true;
			this.type = this.info.type;
			this.marketClassify = {
				money: ['松货币', '紧货币', '松信用', '紧信用'],
				bull: ['股牛债牛', '股牛债平', '股牛债熊', '股平债牛', '股平债平', '股平债熊', '股熊债牛', '股熊债平', '股熊债熊'],
				style: ['成长强势', '价值强势', '大盘强势', '小盘强势'],
				secondary: ['上升', '牛市', '震荡', '熊市', '反弹', '大跌'],
				inventory: ['被动去库存', '主动补库存', '被动补库存', '主动去库存'],
				macro: ['复苏', '繁荣', '滞涨', '衰退']
			};
			this.classifyTranseName = {
				money: '货币周期',
				bull: '牛熊市场周期',
				style: '市场风格',
				secondary: '二级市场周期',
				inventory: '库存周期',
				macro: '宏观周期'
			};
			this.marketClassList = [];
			for (const key in this.classifyTranseName) {
				this.marketClassList.push({
					label: this.classifyTranseName[key],
					value: key,
					show: true,
					color: this.colorList[key]
				});
			}
			this.marketData = { market_rank: [], market_name: [] };
			data.map((item) => {
				this.marketData.market_rank.push(item.windowRank);
				this.marketData.market_name.push(item.description);
			});
			this.formatMarket();
		},
		// 格式化市场适应性
		formatMarket() {
			if (this.marketData?.market_rank && this.marketData?.market_name) {
				let rank = [];
				let description = [];
				let obj = {
					rank: this.marketData?.market_rank,
					description: this.marketData?.market_name
				};
				obj.rank?.map((item, index) => {
					if (item * 1) {
						rank.push(item);
						description.push(obj.description[index]);
					}
				});
				let industryList = [];
				for (const key in this.marketClassify) {
					if (this.marketClassify[key]) {
						industryList.push(...this.marketClassify[key]);
					}
				}
				let newRank = [];
				let newDes = [];
				description.map((item, index) => {
					if (industryList.indexOf(item) != -1) {
						newDes.push(item);
						newRank.push(rank[index]);
					}
				});
				this.hangyeop = this.drawChart(newRank, newDes);
			}
		},
		// 画图
		drawChart(market, name) {
			let nameList = [];
			let dataList = [];
			let backgroundList = {};
			let backIndicator = Array.from({ length: name.length }, (item) => (item = { name: '', max: 1 }));
			for (let classify_name in this.marketClassify) {
				let theClassNum = 0;
				let classify = this.marketClassify[classify_name];
				backgroundList[classify_name] = [];
				classify.forEach((item) => {
					if (name.includes(item)) {
						theClassNum++;
						let index = name.indexOf(item);
						nameList.push({ name: item, max: 1 });
						dataList.push(market[index]);
						backgroundList[classify_name].push(item);
					}
				});
				let zhName = this.classifyTranseName[classify_name];
				let zhIndex = Math.ceil(nameList.length - 1 - theClassNum / 2);
				// if (theClassNum > 0) {
				// 	backIndicator[zhIndex].name = zhName;
				// }
			}
			let option = {
				radar: [
					{
						show: true,
						// 底色
						startAngle: 90 + (360 / nameList.length) * 1,
						indicator: backIndicator,
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#A1A1A1',
								// 分隔线颜色
								width: 1,
								// 分隔线线宽
								type: [5]
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(255,255,255)']
								// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#A1A1A1',
								// 坐标轴线线的颜色。
								width: 1,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						center: ['center', 'center'],
						name: {
							textStyle: {
								fontSize: '12px',
								color: 'rgba(0,0,0,0.65)',
								fontWeight: 400
							}
						},
						nameGap: 6,
						radius: 130
					},
					{
						show: true,
						// 数据
						startAngle: 90 + 360 / nameList.length,
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#A1A1A1',
								// 分隔线颜色
								width: 1,
								// 分隔线线宽
								type: [5]
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(255,255,255)']
								// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#A1A1A1',
								// 坐标轴线线的颜色。
								width: 1,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						center: ['center', 'center'],
						name: {
							textStyle: {
								fontSize: '12px',
								color: 'rgba(0,0,0,0.65)',
								fontWeight: 400
							}
						},
						indicator: nameList,
						nameGap: 6,
						radius: 130
					}
				],

				// toolbox: {
				// 	show: true,
				// 	feature: {
				// 		saveAsImage: { pixelRatio: 3 }
				// 	}
				// },
				series: [
					{
						// 数据
						itemStyle: {
							// 单个拐点标志的样式设置。
							normal: {
								color: '#ffffff',
								borderColor: '#4096ff',
								// 拐点的描边颜色。[ default: '#000' ]
								borderWidth: 2
								// 拐点的描边宽度，默认不描边。[ default: 0 ]
							}
						},
						lineStyle: {
							// 单项线条样式。
							normal: {
								color: '#4096ff',
								opacity: 1 // 图形透明度
							}
						},
						areaStyle: {
							// 单项区域填充样式
							normal: {
								color: '#4096ff', // 填充的颜色。[ default: "#000" ]
								opacity: 0.2
							}
						},
						name: '基金经理能力',
						type: 'radar',
						data: [
							{
								value: dataList,
								name: '基金经理能力'
							}
						],
						radarIndex: 1
					}
				]
			};
			let onlyNameList = nameList.map((item) => item.name);
			let seriesData = {
				// 底色
				type: 'radar',
				radarIndex: 0,
				data: []
			};
			for (let classify_name in this.marketClassify) {
				let classify = this.marketClassify[classify_name];
				let arr = Array.from({ length: dataList.length }, (item) => (item = ''));
				classify.forEach((item) => {
					if (onlyNameList.includes(item)) {
						let index = onlyNameList.indexOf(item);
						arr[index] = 1;
					}
				});
				// 需要每个色块往前加一位与前面的色块连上
				let preIndex = arr.indexOf(1);
				preIndex === 0 ? (arr[arr.length - 1] = 1) : (arr[preIndex - 1] = 1);

				seriesData.data.push({
					name: classify_name,
					symbol: 'none',
					value: arr,
					areaStyle: { color: this.colorList[classify_name], opacity: 0.2 },
					itemStyle: { color: this.colorList[classify_name] },
					lineStyle: { width: 0 }
				});
			}
			// if (nameList.length > 4) {
			this.classShow = true;
			option.series.unshift(seriesData);
			// } else {
			// 	this.classShow = false;
			// 	option = {
			// 		color: ['#4096FF'],
			// 		xAxis: {
			// 			margin: 12,
			// 			type: 'category',
			// 			data: name,
			// 			axisLabel: {
			// 				show: true,
			// 				color: 'rgba(0, 0, 0, 0.65)',
			// 				textStyle: {
			// 					fontSize: '12px'
			// 				}
			// 				// interval: 0
			// 			},
			// 			axisTick: {
			// 				show: false
			// 			},
			// 			axisLine: {
			// 				lineStyle: {
			// 					color: '#e9e9e9'
			// 				}
			// 			}
			// 		},
			// 		grid: {
			// 			left: '0',
			// 			right: '0',
			// 			bottom: '0',
			// 			top: '24px',
			// 			containLabel: true
			// 		},

			// 		yAxis: {
			// 			margin: 16,
			// 			axisLine: {
			// 				show: false
			// 			},
			// 			axisTick: {
			// 				show: false
			// 			},
			// 			axisLabel: {
			// 				textStyle: {
			// 					fontSize: '12px'
			// 				}
			// 			},
			// 			splitLine: {
			// 				show: true,
			// 				lineStyle: {
			// 					type: 'dashed'
			// 				}
			// 			},
			// 			type: 'value'
			// 		},
			// 		tooltip: {
			// 			textStyle: {
			// 				fontSize: '12px'
			// 			},
			// 			trigger: 'axis'
			// 		},
			// 		series: [
			// 			{
			// 				barWidth: '20px',
			// 				data: market,
			// 				type: 'bar',
			// 				symbol: 'none'
			// 			}
			// 		]
			// 	};
			// }
			return option;
		},
		// 切换显示大类
		changeSwitch(item) {
			this.$set(
				this.marketClassList,
				this.marketClassList.findIndex((obj) => {
					return obj.value == item.value;
				}),
				{ ...item, show: !item.show }
			);
			this.marketClassify = {
				money: ['松货币', '紧货币', '松信用', '紧信用'],
				bull: ['股牛债牛', '股牛债平', '股牛债熊', '股平债牛', '股平债平', '股平债熊', '股熊债牛', '股熊债平', '股熊债熊'],
				style: ['成长强势', '价值强势', '大盘强势', '小盘强势'],
				secondary: ['上升', '牛市', '震荡', '熊市', '反弹', '大跌'],
				inventory: ['被动去库存', '主动补库存', '被动补库存', '主动去库存'],
				macro: ['复苏', '繁荣', '滞涨', '衰退']
			};
			let classifyTranseName = {};
			let marketClassify = {};
			this.marketClassList.map((item) => {
				if (item.show) {
					classifyTranseName[item.value] = item.label;
					marketClassify[item.value] = this.marketClassify[item.value];
				}
			});
			this.classifyTranseName = classifyTranseName;
			this.marketClassify = marketClassify;
			this.formatMarket();
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				setTimeout(() => {
					this.$nextTick(async () => {
						let key = 'marketCapacity' + (this.showDescription ? '' : 'Description');
						let height = document.getElementById(key).clientHeight;
						let width = document.getElementById(key).clientWidth;
						let canvas = await this.html2canvas(document.getElementById(key), {
							scale: 3
						});
						resolve([
							...this.$exportWord.exportTitle('市场适应性'),
							...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
								width,
								height
							})
						]);
					});
				}, 1000);
			});
		}
	}
};
</script>

<style scoped>
.charts_two_class {
	height: 260px;
}
</style>
