const moment = require('moment');
/**
 *
 * @param {折线图}
 * color: 接收一个颜色数组(不传递使用默认) Array
 * tooltip: 接收一个function(不传递为无) Function
 * dataZoom: 接收一个布尔值(默认为false,不显示) Boolean
 * legend: 接收一个数组(默认为空) Array
 * xAxis: 接收一个对象({name(String,默认不显示),type(String,默认category),formatter(Function,默认为空),boundaryGap(Boolean,默认为false),isAlign(Boolean,默认为false),data(Array,必传)})
 * yAxis: 接收一个对象({name(String,默认不显示),type(String,默认value),formatter(Function,默认为空),data(Array,默认为空)})
 * series: 接收一个数组
 * @returns
 */
export function lineChartOption({ toolbox, color, tooltip, dataZoom, legend, xAxis, yAxis, series, grid, title }) {
	if (!xAxis || !xAxis?.length || typeof xAxis !== 'object') {
		return;
	}
	if (!series || !series.length || typeof series !== 'object') {
		return;
	}
	let option = {
		toolbox: {
			feature: {
				// dataZoom: {
				// 	yAxisIndex: 'none'
				// },
				// dataView: { readOnly: false },
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: {}
			},
			top: -4,
			width: 104
		}
	};
	option['toolbox'] =
		toolbox == 'none'
			? {}
			: toolbox == false
				? {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					}
				: {
						feature: {
							magicType: { type: ['line', 'bar'] },
							restore: {},
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					};
	option['color'] = color?.length
		? color
		: [
				'#4096ff',
				'#FEC70B',
				'#FDED00',
				'#B5EC30',
				'#08C47C',
				'#00D7E9',
				'#984dc1',
				'#984dc1',
				'#984dc1',
				'#984dc1',
				'#191970',
				'#B22222',
				'#008080',
				'#FF8C00',
				'#4B0082',
				'#FF6B6B',
				'#26619C',
				'#CC7722',
				'#FF00FF',
				'#01796F',
				'#9370DB',
				'#BA55D3',
				'#FF1493',
				'#C71585',
				'#4B0082',
				'#9932CC',
				'#8A2BE2',
				'#DA70D6',
				'#FF00FF',
				'#8B008B'
			];
	option['tooltip'] = {
		trigger: 'axis',
		padding: 0,
		backgroundColor: tooltip?.backgroundColor ? tooltip?.backgroundColor : '#ffffff',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'line'
		},
		textStyle: tooltip?.textStyle ? tooltip?.textStyle : { color: '#000000' }
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					show: dataZoom?.show == false ? false : true,
					type: dataZoom?.start || 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					bottom: dataZoom?.bottom ? dataZoom?.bottom : '0',
					start: dataZoom?.start || 0,
					end: dataZoom?.end || 100
				}
			])
		: '';
	option['grid'] = {
		show: true,
		borderWidth: 0,
		backgroundColor: '#ecf5ff',
		left: grid?.left ? grid?.left : '16px',
		right: grid?.right
			? grid?.right
			: (xAxis?.[0]?.name && xAxis?.[0]?.show !== false) || (yAxis?.length >= 2 ? (yAxis?.[1]?.show == false ? false : true) : false)
				? '32px'
				: '0',
		bottom: grid?.bottom ? grid?.bottom : dataZoom ? '64px' : '18px',
		top: grid?.top
			? grid?.top
			: (legend && legend?.length) ||
				  yAxis.some((item) => {
						return item.name;
				  })
				? '38px'
				: '18px', // 无图例18px
		containLabel: grid?.containLabel == false ? false : true
	};
	option['legend'] =
		legend?.type !== 'plain'
			? {
					type: 'scroll',
					pageIcons: {
						horizontal: [
							'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
							'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
						]
					},
					itemWidth: 12,
					itemHeight: 8,
					textStyle: {
						color: 'rgba(0, 0, 0, 0.65)',
						fontSize: 12,
						fontWeight: 400
					},
					data: legend?.length
						? legend.map((item) => {
								if (item?.name) {
									return {
										...item
										// icon: 'path://M63.6 489.6h896.7v44.8H63.6z'
									};
								} else {
									return item;
								}
							})
						: legend?.data?.length
							? legend?.data.map((item) => {
									if (item?.name) {
										return {
											...item
											// icon: 'path://M63.6 489.6h896.7v44.8H63.6z'
										};
									} else {
										return item;
									}
								})
							: [],
					icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
					itemGap: legend?.itemGap ? legend?.itemGap : 40,
					right: legend?.right ? legend?.right : 128,
					width: legend?.width ? legend?.width : '80%',
					left: legend?.left ? legend?.left : 'center',
					orient: legend?.orient ? legend?.orient : 'horizontal',
					selected: legend?.selected ? legend?.selected : {},
					pageButtonGap: 16,
					top: legend?.top,
					bottom: legend?.bottom
				}
			: {
					type: 'plain',
					itemWidth: 12,
					itemHeight: 8,
					textStyle: {
						color: 'rgba(0, 0, 0, 0.65)',
						fontSize: 12,
						fontWeight: 400
					},
					data: legend?.length
						? legend.map((item) => {
								if (item?.name) {
									return { ...item };
								} else {
									return item;
								}
							})
						: legend?.data?.length
							? legend?.data.map((item) => {
									if (item?.name) {
										return { ...item };
									} else {
										return item;
									}
								})
							: [],
					width: legend?.width ? legend?.width : '80%',
					itemWidth: 18,
					itemHeight: 10,
					orient: 'vertical',
					left: '0%',
					icon: 'roundRect'
				};

	option['xAxis'] = xAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			// offset: 8,
			nameGap: 8,
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			type: item?.type ? item?.type : 'category',
			boundaryGap: item?.boundaryGap ? item?.boundaryGap : false,
			axisLine: {
				onZero: false,
				lineStyle: {
					color: '#e9e9e9'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				showMinLabel: true,
				showMaxLabel: true,
				hideOverlap: true,
				formatter: item?.formatter ? item?.formatter : undefined,
				rotate: item?.rotate ? item?.rotate : 0,
				interval: item.interval ? item.interval : 'auto'
			},
			min: 'dataMin',
			max: 'dataMax',
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
					})
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			inverse: item.inverse ? item.inverse : false,
			scale: item?.scale == false ? false : true,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				align: item.nameTextStyle?.align ? item.nameTextStyle.align : 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			max: typeof item?.max == 'number' ? item.max : null,
			min: typeof item?.min == 'number' ? item.min : null,
			nameLocation: item.nameLocation ? item.nameLocation : 'end',
			nameGap: item.nameGap ? item.nameGap : '15',
			nameRotate: item.nameRotate ? item.nameRotate : 'null',
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	option['title'] = title ? title : {};
	return option;
}
/**
 *
 * @param {柱状图}
 * color: 接收一个颜色数组(不传递使用默认) Array
 * tooltip: 接收一个function(不传递为无) Function
 * dataZoom: 接收一个布尔值(默认为false,不显示) Boolean
 * legend: 接收一个数组(默认为空) Array
 * xAxis: 接收一个对象({name(String,默认不显示),type(String,默认category),formatter(Function,默认为空),boundaryGap(Boolean,默认为false),isAlign(Boolean,默认为false),data(Array,必传)})
 * yAxis: 接收一个对象({name(String,默认不显示),type(String,默认value),formatter(Function,默认为空),data(Array,默认为空)})
 * series: 接收一个数组
 * @returns
 */
export function barChartOption({ toolbox, title, color, tooltip, dataZoom, visualMap, legend, xAxis, yAxis, series, grid }) {
	if (!xAxis || !xAxis?.length || typeof xAxis !== 'object') {
		return;
	}
	if (!series || !series.length || typeof series !== 'object') {
		return;
	}
	let option = {
		toolbox: {
			feature: {
				// dataZoom: {
				// 	yAxisIndex: 'none'
				// },
				// dataView: { readOnly: false },
				magicType: { type: ['line', 'bar'] },
				restore: {},
				saveAsImage: { pixelRatio: 3 }
			},
			top: -4,
			width: 104
		}
	};
	option['title'] = title ? title : {};
	option['toolbox'] =
		toolbox == 'none'
			? {}
			: toolbox == false
				? {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					}
				: {
						feature: {
							magicType: { type: ['line', 'bar'] },
							restore: {},
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					};
	option['color'] = color?.length
		? color
		: [
				'#4096ff',
				'#FEC70B',
				'#FDED00',
				'#B5EC30',
				'#08C47C',
				'#00D7E9',
				'#984dc1',
				'#984dc1',
				'#984dc1',
				'#984dc1',
				'#191970',
				'#B22222',
				'#008080',
				'#FF8C00',
				'#4B0082',
				'#FF6B6B',
				'#26619C',
				'#CC7722',
				'#FF00FF',
				'#01796F',
				'#9370DB',
				'#BA55D3',
				'#FF1493',
				'#C71585',
				'#4B0082',
				'#9932CC',
				'#8A2BE2',
				'#DA70D6',
				'#FF00FF',
				'#8B008B'
			];
	option['tooltip'] = {
		trigger: tooltip?.trigger || 'axis',
		padding: 0,
		backgroundColor: tooltip?.backgroundColor ? tooltip?.backgroundColor : '#ffffff',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'line'
		},
		textStyle: tooltip?.textStyle ? tooltip?.textStyle : { color: '#000000' }
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					show: dataZoom?.show == false ? false : true,
					type: dataZoom?.start || 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					bottom: dataZoom?.bottom ? dataZoom?.bottom : '0',
					start: dataZoom?.start || 0,
					end: dataZoom?.end || 100
				}
			])
		: '';
	visualMap ? (option['visualMap'] = visualMap) : '';
	option['grid'] = {
		show: true,
		borderWidth: 0,
		backgroundColor: '#ecf5ff',
		left: grid?.left ? grid?.left : yAxis?.[0]?.show == false ? '0' : '16px',
		right: grid?.right
			? grid?.right
			: (xAxis?.[0]?.name && xAxis?.[0]?.name !== '') || (yAxis?.length >= 2 ? (yAxis?.[1]?.show == false ? false : true) : false)
				? '32px'
				: '0',
		bottom: grid?.bottom ? grid?.bottom : dataZoom ? '64px' : '18px',
		top: grid?.top
			? grid?.top
			: (legend && legend?.length) ||
				  legend?.data?.length ||
				  yAxis.some((item) => {
						return item.name;
				  })
				? '38px'
				: '18px', // 无图例18px
		containLabel: true
	};
	option['legend'] = {
		type: 'scroll',
		pageIcons: {
			horizontal: [
				'path://M11.7487 6.92214L6.30673 0.634973C6.15096 0.455009 5.85102 0.455009 5.69359 0.634973L0.251579 6.92214C0.049409 7.15658 0.231693 7.5 0.558148 7.5L11.4422 7.5C11.7686 7.5 11.9509 7.15658 11.7487 6.92214Z',
				'path://M0.251255 1.07786L5.69327 7.36503C5.84904 7.54499 6.14898 7.54499 6.30641 7.36503L11.7484 1.07786C11.9506 0.843416 11.7683 0.499999 11.4419 0.5L0.557824 0.5C0.231369 0.5 0.0490849 0.843417 0.251255 1.07786Z'
			]
		},
		itemWidth: 12,
		itemHeight: 8,
		textStyle: {
			color: 'rgba(0, 0, 0, 0.65)',
			fontSize: 12,
			fontWeight: 400
		},
		data: legend?.length
			? legend.map((item) => {
					if (item?.name) {
						return {
							...item,
							icon: item?.icon == 'line' ? 'path://M63.6 489.6h896.7v44.8H63.6z' : 'path://M112 272h800v480H112z'
						};
					} else {
						return item;
					}
				})
			: legend?.data?.length
				? legend?.data.map((item) => {
						if (item?.name) {
							return {
								...item,
								icon: item?.icon == 'line' ? 'path://M63.6 489.6h896.7v44.8H63.6z' : 'path://M112 272h800v480H112z'
							};
						} else {
							return item;
						}
					})
				: [],
		// icon: 'path://M63.6 489.6h896.7v44.8H63.6z',
		itemGap: legend?.itemGap ? legend?.itemGap : 40,
		right: legend?.right ? legend?.right : 128,
		width: legend?.width ? legend?.width : '80%',
		left: legend?.left ? legend?.left : 'center',
		orient: legend?.orient ? legend?.orient : 'horizontal',
		selected: legend?.selected ? legend?.selected : {},
		pageButtonGap: 16
	};
	legend?.top ? (option.legend['top'] = legend?.top) : '';
	legend?.bottom ? (option.legend['bottom'] = legend?.bottom) : '';
	option['xAxis'] = xAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: item?.offset || item?.offset == 0 ? item?.offset : 8,
			nameGap: 8,
			min: item?.min ? item?.min : 'dataMin',
			max: item?.max ? item?.max : 'dataMax',
			name: item?.name ? item?.name : '',
			position: item?.position ? item?.position : 'bottom',
			boundaryGap: item?.boundaryGap == false ? item?.boundaryGap : true,
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			type: item?.type ? item?.type : 'category',
			// boundaryGap: item?.boundaryGap ? item?.boundaryGap : false,
			axisLine: {
				onZero: false,
				lineStyle: {
					color: '#e9e9e9'
				}
			},
			axisLabel: {
				show: item?.axisLabel?.show == false ? false : true,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				showMinLabel: true,
				showMaxLabel: true,
				hideOverlap: true,
				rotate: item?.axisLabelRotate ? item?.axisLabelRotate : 0,
				formatter: item?.formatter ? item?.formatter : undefined,
				rotate: item?.rotate ? item?.rotate : 0
			},
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
					})
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			inverse: item.inverse ? item.inverse : false,
			scale: item?.scale == false ? false : true,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			nameTextStyle: {
				align: item.nameTextStyle?.align ? item.nameTextStyle.align : 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			max: typeof item?.max == 'number' ? item.max : null,
			min: typeof item?.min == 'number' ? item.min : null,
			nameLocation: item.nameLocation ? item.nameLocation : 'end',
			nameGap: item.nameGap ? item.nameGap : '15',
			nameRotate: item.nameRotate ? item.nameRotate : 'null',
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	return option;
}
export function triangleCircleOption({ toolbox, tooltip, dataZoom, xAxis, yAxis, series, grid, visualMap }) {
	let option = {
		visualMap: [
			/**
			 * 负数为绿，正数为红
			 * 对应series数据中数组索引为2的值
			 */
			{
				show: false,
				right: '5px',
				top: '5%',
				dimension: 2,
				min: -1.0,
				max: 1.0,
				itemHeight: 100,
				precision: 1,
				text: ['1', '-1.0'],
				// type: 'piecewise',
				// pieces: [
				//     { gt: 0, color: '#FFDEDC' }, // 大于 0 的部分设置为红色
				//     { lte: 0, color: 'green' } // 小于等于 0 的部分设置为绿色
				// ],
				textGap: 0,
				textStyle: {
					color: 'black',
					fontSize: 10
				},
				inRange: {
					color: ['#BEE6BF', '#54B153', '#1C5E2F', '#FFDEDC', '#D04840', '#661618']
				},
				outOfRange: {
					color: ['rgba(255,255,255,.2)']
				},
				controller: {
					inRange: {
						// color: ['#23C343', '#23C343CC', '#23C34399', '#23C34366', '#F7656066', '#F7656099', '#F76560CC', '#F76560']
						color: ['#BEE6BF', '#54B153', '#1C5E2F', '#FFDEDC', '#D04840', '#661618']
					}
				}
			},
			/**
			 * 数值越大Size越大
			 * 对应series数据中数组索引为3的值
			 */
			{
				show: false,
				right: '0px',
				top: '150px',
				dimension: 3,
				itemWidth: 30,
				itemHeight: 120,
				precision: 0,
				min: visualMap?.min || 0,
				max: visualMap?.max || 100,
				// text: [visualMap?.min || 0, visualMap?.min || 100],
				text: ['配置权重%'],
				textGap: 8,
				textStyle: {
					color: 'black',
					fontSize: 10
				},
				inRange: {
					symbolSize: [5, 24]
				},
				controller: {
					inRange: {
						color: ['black']
					}
				}
			},
			/**
			 * 正数为圆圈，负数为三角
			 * 对应series数据中数组索引为3的值
			 */
			{
				show: false,
				type: 'piecewise', // 定义为分段型 visualMap
				right: '0',
				bottom: '20px',
				dimension: 4,
				splitNumber: 2,
				precision: 1,
				itemWidth: 10,
				itemHeight: 10,
				textGap: 5,
				textStyle: {
					color: 'black'
				},
				// categories 定义了 visualMap-piecewise 组件显示出来的项。
				categories: ['True', 'False'],
				// 表示 目标系列 的视觉样式 和 visualMap-piecewise 共有的视觉样式。
				inRange: {
					symbol: ['triangle', 'circle']
				},
				// 表示 visualMap-piecewise 本身的视觉样式。
				controller: {
					inRange: {
						color: ['black', 'black'],
						symbol: ['triangle', 'circle']
					}
				}
			}
		]
	};
	option['toolbox'] =
		toolbox == 'none'
			? {}
			: toolbox == false
				? {
						feature: {
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					}
				: {
						feature: {
							magicType: { type: ['line', 'bar'] },
							restore: {},
							saveAsImage: { pixelRatio: 3 }
						},
						top: -4,
						width: 104
					};
	option['grid'] = {
		show: true,
		borderWidth: 0,
		backgroundColor: '#ffffff',
		// backgroundColor: '#fff8f0',
		left: grid?.left ? grid?.left : yAxis?.[0]?.show == false ? '0' : '16px',
		right: grid?.right
			? grid?.right
			: (xAxis?.[0]?.name && xAxis?.[0]?.name !== '') || (yAxis?.length >= 2 ? (yAxis?.[1]?.show == false ? false : true) : false)
				? '32px'
				: '8px',
		bottom: grid?.bottom ? grid?.bottom : dataZoom ? '64px' : '18px',
		top: grid?.top ? grid?.top : '18px', // 无图例18px
		containLabel: true
	};
	option['tooltip'] = {
		backgroundColor: '#fff',
		trigger: tooltip?.trigger || 'axis',
		formatter: tooltip?.formatter ? tooltip?.formatter : undefined,
		axisPointer: {
			type: tooltip?.type ? tooltip?.type : 'cross'
		},
		textStyle: tooltip?.textStyle ? tooltip?.textStyle : { color: '#000000' }
	};
	dataZoom
		? (option['dataZoom'] = [
				{
					show: dataZoom?.show == false ? false : true,
					type: dataZoom?.start || 'slider',
					zoomOnMouseWheel: false,
					preventDefaultMouseMove: false,
					bottom: dataZoom?.bottom ? dataZoom?.bottom : '0',
					start: dataZoom?.start || 0,
					end: dataZoom?.end || 100
				}
			])
		: '';
	option['xAxis'] = xAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 8,
			min: item?.min ? item?.min : 'dataMin',
			max: item?.max ? item?.max : 'dataMax',
			name: item?.name ? item?.name : '',
			position: item?.position ? item?.position : 'bottom',
			triggerEvent: item?.triggerEvent ? item?.triggerEvent : false,
			nameTextStyle: {
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			type: item?.type ? item?.type : 'category',
			boundaryGap: item?.boundaryGap ? item?.boundaryGap : false,
			axisLine: {
				onZero: false,
				lineStyle: {
					color: '#e9e9e9'
				}
			},
			splitLine: {
				show: true,
				interval: 0,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed',
					width: 1
				}
			},
			axisLabel: {
				show: item?.axisLabel?.show == false ? false : true,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				showMinLabel: true,
				showMaxLabel: true,
				hideOverlap: true,
				rotate: item?.axisLabelRotate ? item?.axisLabelRotate : 0,
				formatter: item?.formatter ? item?.formatter : undefined,
				rotate: item?.rotate ? item?.rotate : 0
			},
			data: item?.isAlign
				? item?.data
				: item?.data?.map((obj, index) => {
						if (index == 0 || index == item.data?.length - 1) {
							return {
								value: obj,
								textStyle: { align: index == 0 ? 'left' : 'right' }
							};
						} else {
							return obj;
						}
					})
		};
	});
	option['yAxis'] = yAxis?.map((item) => {
		return {
			show: item?.show == false ? false : true,
			offset: 8,
			nameGap: 20,
			inverse: item.inverse ? item.inverse : false,
			scale: item?.scale == false ? false : true,
			type: item?.type ? item?.type : 'value',
			name: item?.name ? item?.name : '',
			triggerEvent: item?.triggerEvent ? item?.triggerEvent : false,
			nameTextStyle: {
				align: item.nameTextStyle?.align ? item.nameTextStyle.align : 'left',
				fontFamily: 'PingFang',
				fontStyle: 'normal',
				fontWeight: 400,
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)'
			},
			max: typeof item?.max == 'number' ? item.max : null,
			min: typeof item?.min == 'number' ? item.min : null,
			nameLocation: item.nameLocation ? item.nameLocation : 'end',
			nameGap: item.nameGap ? item.nameGap : '15',
			nameRotate: item.nameRotate ? item.nameRotate : 'null',
			interval: item.interval ? item.interval : 1,
			axisLine: {
				show: false
			},
			axisTick: {
				show: false
			},
			splitLine: {
				show: item?.splitLine == false ? false : true,
				lineStyle: {
					color: '#e9e9e9',
					type: 'dashed'
				}
			},
			axisLabel: {
				fontSize: 12,
				color: 'rgba(0, 0, 0, 0.65)',
				formatter: item?.formatter ? item?.formatter : undefined
			},
			data: item?.data ? item?.data : undefined
		};
	});
	option['series'] = series;
	return option;
}
/**
 *
 * @param {盒须图}
 * data:接收一个对象数组 例：[
	{
		"date": "2000",
		"value": "208.4968974"
	},
	{


		"date": "2000",
		"value": "160.5328879"
	}]
 * @returns
 */
export function boxplotOption(data) {
	let series = [];
	let yAxis = [];
	let grid = [];
	let xAxis = [];
	let scatter = [];

	let columns = [...new Set(data.map((v) => v.label))];
	let date_list = [...new Set(...data.map((item) => item.value.map((v) => v.date)))].sort((a, b) => {
		return moment(moment(a, 'YYYY QQ').format()).isAfter(moment(b, 'YYYY QQ').format()) ? -1 : 1;
	});
	date_list.map((item, index) => {
		// grid
		let ave = 100 / date_list.length;
		grid.push({
			containLabel: true,
			left: ave * index + '%',
			right: ave * (date_list.length - 1 - index) + '%'
		});
		// x轴
		xAxis.push({
			z: 2,
			type: 'value',
			gridIndex: index,
			// name: '(元/㎡)',
			nameTextStyle: {
				color: '#999',
				padding: [0, 30, 0, 0]
			},
			axisLine: {
				show: false
			},
			min: 0,
			max: 100,
			axisTick: {
				show: false
			},
			splitLine: {
				lineStyle: {
					color: ['#EEE']
				}
			},
			axisLabel: {
				color: '#999'
			}
		});
		// y轴
		yAxis.push({
			name: item,
			axisLabel: { show: index == 0 ? true : false },
			gridIndex: index,
			type: 'category',
			data: columns,
			boundaryGap: true,
			nameGap: 30,
			splitArea: {
				show: false
			},
			axisLabel: {
				color: '#999'
			},
			splitLine: {
				show: false
			},
			axisLine: {
				lineStyle: {
					color: ['#ccc']
				}
			},
			axisTick: {
				show: false
			}
		});
		// 盒须图数据
		series.push({
			name: item,
			type: 'boxplot',
			z: 5,
			xAxisIndex: index,
			yAxisIndex: index,
			tooltip: {
				formatter: function (param) {
					return [
						'最大值: ' + param?.data[5].toFixed(2),
						'75%分位' + param?.data[4].toFixed(2),
						'50%分位: ' + param?.data[3].toFixed(2),
						'25%分位: ' + param?.data[2].toFixed(2),
						'最小值: ' + param?.data[1].toFixed(2)
					].join('<br/>');
				}
			},
			data: [...setBoxplot(data, item)]
		});
		// 中位数数据
		scatter.push({
			name: '平均数',
			xAxisIndex: index,
			yAxisIndex: index,
			type: 'scatter',
			z: 10,
			data: setBoxplotScatter(data, item)
		});
	});
	// 指定图表的配置项和数据
	const option = {
		grid,
		tooltip: {},
		toolbox: {
			feature: {
				saveAsImage: { pixelRatio: 3 }
			}
		},
		yAxis,
		xAxis,
		series: [...scatter, ...series]
	};
	return option;
}
/**
 *
 * @param {小提琴图}
 * data:接收一个对象数组 例：[
	{
		"date": "2000",
		"value": "208.4968974"
	},
	{
		"date": "2000",
		"value": "160.5328879"
	}]
 * @returns
 */
export function violinOption(data, scatter_data) {
	let columns = [...new Set(data.map((v) => v.label))];
	let date_list = [...new Set(...data.map((item) => item.value.map((v) => v.date)))].sort((a, b) => {
		return moment(moment(a, 'YYYY QQ').format()).isAfter(moment(b, 'YYYY QQ').format()) ? -1 : 1;
	});
	let violin = [];
	let yAxis = [];
	let grid = [];
	let xAxis = [];
	date_list.map((date, index) => {
		// 小提琴
		violin.push(setViolin(date, formatViolinData(data, date)?.[0].value, index));
	});
	date_list.map((item, index) => {
		// grid
		let ave = 100 / date_list.length;
		grid.push({
			containLabel: true,
			left: ave * index + '%',
			right: ave * (date_list.length - 1 - index) + '%'
		});
		// x轴
		xAxis.push({
			z: 2,
			type: 'value',
			gridIndex: index,
			// name: '(元/㎡)',
			nameTextStyle: {
				color: '#999',
				padding: [0, 30, 0, 0]
			},
			axisLine: {
				show: false
			},
			min: 0,
			max: 100,
			axisTick: {
				show: false
			},
			splitLine: {
				lineStyle: {
					color: ['#EEE']
				}
			},
			axisLabel: {
				color: '#999'
			}
		});
		// y轴
		yAxis.push({
			name: item,
			axisLabel: { show: index == 0 ? true : false },
			gridIndex: index,
			type: 'category',
			data: columns,
			boundaryGap: true,
			nameGap: 30,
			splitArea: {
				show: false
			},
			axisLabel: {
				color: '#999'
			},
			splitLine: {
				show: false
			},
			axisLine: {
				lineStyle: {
					color: ['#ccc']
				}
			},
			axisTick: {
				show: false
			}
		});
	});
	// 设置小提琴
	let scatter = [];
	let all_scatter = [];
	scatter_data.map((obj) => {
		obj.value.map((item) => {
			all_scatter.push({ ...item, label: obj.label });
		});
	});
	date_list.map((date, index) => {
		scatter.push(
			...setScatter(
				all_scatter.filter((v) => v.date == date),
				index
			)
		);
	});
	// 指定图表的配置项和数据
	const option = {
		grid,
		tooltip: {},
		toolbox: {
			feature: {
				saveAsImage: { pixelRatio: 3 }
			}
		},
		yAxis,
		xAxis,
		series: [...violin, ...scatter]
	};
	return option;
}
function setBoxplot(data, quarter) {
	let result = data.map((item, index) => {
		let array = item.value.filter((v) => v.date == quarter).map((v) => v.value);
		// 最小值
		var minValue = Math.min(...array);

		// 排序数组
		var sortedArray = array.slice().sort((a, b) => a - b);

		// 25%分位数
		var q1 = sortedArray[Math.floor(sortedArray.length * 0.25)];

		// 中位数（50%分位数）
		var median = sortedArray[Math.floor(sortedArray.length * 0.5)];

		// 75%分位数
		var q3 = sortedArray[Math.floor(sortedArray.length * 0.75)];

		// 最大值
		var maxValue = Math.max(...array);
		return [minValue, q1, median, q3, maxValue];
	});
	return result;
}
function setViolin(name, data, index) {
	function kernelDensityEstimator(kernel, X) {
		return function (V) {
			return X.map(function (x) {
				return [
					x,
					d3.mean(V, function (v) {
						return kernel(x - v);
					})
				];
			});
		};
	}
	function kernelEpanechnikov(k) {
		return function (v) {
			return Math.abs((v /= k)) <= 1 ? (0.75 * (1 - v * v)) / k : 0;
		};
	}
	const d3 = Object.assign({}, require('d3-shape'), require('d3-scale'), require('d3-array'), require('d3-color'));
	const columns = [...new Set(data.map((v) => v.date))];

	const dataSource = columns.map((date) => data.filter((item) => item.date === date).map((item) => item.value));
	return {
		type: 'custom',
		color: ['#B1D0FA'],
		xAxisIndex: index,
		yAxisIndex: index,
		tooltip: { show: false },
		name,
		renderItem: (params, api) => {
			const categoryIndex = api.value(0);

			const min = Math.min(...dataSource[categoryIndex]);
			const max = Math.max(...dataSource[categoryIndex]);
			const liner = d3
				.scaleLinear()
				.domain([min - 50, max + 50])
				.ticks(20);
			let density = kernelDensityEstimator(kernelEpanechnikov(7), liner)(dataSource[categoryIndex]);

			const maxDens = Math.max(...density.map((v) => v[1]));

			const points = density.map((v) => {
				const [y, dens] = v;
				const point = api.coord([y > 100 ? 100 : y < 0 ? 0 : y, categoryIndex]); // 修改这里：交换x和y的值
				point[1] += (((api.size([0, 0])[1] / 2) * dens) / maxDens) * 0.85; // 修改这里：使用point[1]代替point[0]
				return point;
			});

			const points2 = density.map((v) => {
				const [y, dens] = v;
				const point = api.coord([y > 100 ? 100 : y < 0 ? 0 : y, categoryIndex]); // 修改这里：交换x和y的值
				point[1] -= (((api.size([0, 0])[1] / 2) * dens) / maxDens) * 0.85; // 修改这里：使用point[1]代替point[0]
				return point;
			});

			const lineGenerator = d3.line().curve(d3.curveBasis);
			const pathData = lineGenerator(points);
			const pathData2 = lineGenerator(points2);

			return {
				z: 2,
				type: 'path',
				shape: {
					pathData: pathData + pathData2
				},
				style: api.style({
					// fill: api.visual('color'),
					fill: '#4096ff1A',
					stroke: '#4096ff',
					lineWidth: 1
				}),
				styleEmphasis: api.style({
					fill: d3.color('#4096ff1A').darker(0.05),
					// fill: d3.color(api.visual('color')).darker(0.05),
					stroke: d3.color('#4096ff').darker(0.05),
					lineWidth: 2
				})
			};
		},
		encode: {
			y: 0, // 修改这里：将x更改为y
			x: dataSource[ // 修改这里：将y更改为x
				d3.scan(dataSource, function (a, b) {
					return b.length - a.length;
				})
			]
				.map((v, i) => i + 1)
		},
		data: dataSource.map((v, i) => [i, ...v])
	};
}
function calculateAverage(arr) {
	if (arr.length === 0) {
		return 0; // 空数组的平均值为0
	}

	var sum = 0;
	for (var i = 0; i < arr.length; i++) {
		sum += arr[i];
	}

	var average = sum / arr.length;
	return average;
}
function setBoxplotScatter(data, quarter) {
	let results = data.map((item) => {
		return [(calculateAverage(item.value.filter((v) => v.date == quarter).map((v) => v.value)) * 1).toFixed(2), item.label];
	});
	return results;
}
function setScatter(scatter_data, i) {
	let scatter = [];
	scatter_data.map((item) => {
		let index = scatter.findIndex((obj) => {
			return obj.name == item.name;
		});
		if (index == -1) {
			let min = Math.min(...scatter_data.filter((v) => v.date == item.date).map((v) => v.value));
			let max = Math.max(...scatter_data.filter((v) => v.date == item.date).map((v) => v.value));
			scatter.push({
				name: item.name,
				xAxisIndex: i,
				yAxisIndex: i,
				type: 'scatter',
				data: [[(item.value * 1).toFixed(2), item.label]],
				symbol:
					item?.flag == 1
						? 'path://M12.4156 4.82735L8.94433 4.32286L7.39258 1.17696C7.35019 1.09083 7.28047 1.0211 7.19433 0.978716C6.97832 0.872075 6.71582 0.960943 6.60781 1.17696L5.05605 4.32286L1.58476 4.82735C1.48906 4.84102 1.40156 4.88614 1.33457 4.9545C1.25358 5.03774 1.20895 5.14973 1.21049 5.26586C1.21203 5.38199 1.25961 5.49276 1.34277 5.57383L3.8543 8.02247L3.26094 11.4801C3.24702 11.5605 3.25592 11.6432 3.28663 11.7189C3.31733 11.7945 3.36862 11.86 3.43466 11.908C3.50071 11.9559 3.57887 11.9845 3.66029 11.9903C3.74171 11.9961 3.82313 11.9789 3.89531 11.9408L7.00019 10.3084L10.1051 11.9408C10.1898 11.9859 10.2883 12.001 10.3826 11.9846C10.6205 11.9436 10.7805 11.718 10.7395 11.4801L10.1461 8.02247L12.6576 5.57383C12.726 5.50684 12.7711 5.41934 12.7848 5.32364C12.8217 5.08438 12.6549 4.8629 12.4156 4.82735Z'
						: 'circle',
				itemStyle: {
					color: item.flag == 1 ? '#FFD600' : item.color
				},
				markLine: {
					silent: true,
					animation: false,
					lineStyle: {
						type: 'solid',
						color: '#4096ff'
					},
					emphasis: {
						lineStyle: {
							color: '#4096ff',
							width: 2
						}
					},
					data: [
						[
							{
								coord: [min, item.date],
								symbol: 'none'
							},
							{
								coord: [max, item.date],
								symbol: 'none'
							}
						]
					]
				}
			});
		} else {
			scatter[index].data.push([(item.value * 1).toFixed(2), item.label]);
		}
	});
	return scatter;
}
function formatViolinData(data, date) {
	let result = [];
	data.map((item) => {
		item.value.map((obj) => {
			let index = result.findIndex((v) => v.label == obj.date);
			if (index == -1) {
				result.push({
					label: obj.date,
					value: [{ ...obj, date: item.label }]
				});
			} else {
				result[index].value.push({ ...obj, date: item.label });
			}
		});
	});
	return result.filter((v) => v.label == date);
}
