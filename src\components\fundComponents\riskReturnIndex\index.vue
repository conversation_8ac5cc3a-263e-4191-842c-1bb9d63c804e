<template>
  <div class="chart_one"
       v-loading="loadyeji">
    <div class="title"
         style="display: flex; justify-content: space-between; align-items: center">
      <span>风险收益指标&nbsp;&nbsp;{{ description }} </span>
      <el-button class="print_show"
                 icon="el-icon-document-delete"
                 @click="exportExcel">导出Excel</el-button>
    </div>
    <el-table :data="df_measure"
              style="margin-top: 24px"
              class="table"
              :cell-style="elcellstyle"
              ref="multipleTable"
              header-cell-class-name="table-header">
      <el-table-column v-for="item in columns"
                       :key="item.value"
                       align="gotoleft"
                       :prop="item.value"
                       :label="item.label"
                       :sortable="item.sortable">
        <template #header>
          <long-table-popover-chart v-if="item.popover"
                                    :data="foramtPopoverData()"
                                    date_key="year"
                                    :data_key="item.value"
                                    :show_name="item.label">
            <span>{{ item.label }}</span>
          </long-table-popover-chart>
          <span v-else>{{ item.label }}</span>
        </template>
        <template slot-scope="{ row }">
          <span>{{ item.format ? item.format(row[item.value]) : row[item.value] }}</span>
        </template>
      </el-table-column>
      <template slot="empty">
        <el-empty image-size="160"></el-empty>
      </template>
    </el-table>
  </div>
</template>

<script>
import { exportTitle, exportTable } from '@/utils/exportWord.js';
import { filter_json_to_excel } from '@/utils/exportExcel.js';
// 风险收益指标
export default {
  name: 'riskReturnIndex',
  data () {
    return {
      description: null,
      df_measure: [],
      loadyeji: true,
      columns: [
        {
          label: '年份',
          value: 'year',
          sortable: true,
          popover: false
        },
        {
          label: '年累计收益',
          value: 'cum_return',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '排名',
          value: 'cum_return_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '最大回撤',
          value: 'maxdrawdown',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '排名',
          value: 'maxdrawdown_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '波动率',
          value: 'volatility',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '排名',
          value: 'volatility_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '夏普率',
          value: 'sharpe',
          sortable: true,
          popover: true,
          format: this.fix3
        },
        {
          label: '排名',
          value: 'sharpe_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        }
      ]
    };
  },
  methods: {
    // 获取数据
    getData (data, info) {
      console.log(data,info,'ssssssssssss')
      this.fundindextype = info?.type;
      let arr = [];
      data.map((item) => {
        let index = arr.findIndex((obj) => {
          return obj.year == item.year;
        });
        let obj = {};
        obj[item.measure] = item.meter;
        obj[item.measure + '_rank'] = item.measure == 'maxdrawdown' || item.measure == 'volatility' ? 1 - item.rank : item.rank;
        obj[item.measure + '_description'] = item.description;
        if (index == -1) {
          arr.push({
            year: item.year,
            data: obj
          });
        } else {
          arr[index].data = { ...arr[index].data, ...obj };
        }
      });

      this.df_measure = arr.map((item) => {
        return { year: item.year, ...item.data };
      }); //风险收益指标
      console.log(this.df_measure);
      // this.description = data.describe; //风险收益指标描述
      this.loadyeji = false;
    },
    formatColumn () {
      let indexList = [
        {
          label: '跟踪误差',
          value: 'trackingerror',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '排名',
          value: 'trackingerror_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        }
      ];
      let enhanceList = [
        {
          label: '信息比率',
          value: 'information',
          sortable: true,
          popover: true,
          format: this.fix2p
        },
        {
          label: '排名',
          value: 'information_rank',
          sortable: true,
          popover: true,
          format: this.fix2p
        }
      ];
      if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
        this.columns.push(...indexList);
      } else if ((this.fundindextype = 'equityenhance')) {
        this.columns.push(...enhanceList);
      }
    },
    // 隐藏loading
    hideLoading () {
      this.loadyeji = false;
    },
    // df_measure
    foramtPopoverData () {
      let data = [];
      this.df_measure.map((item) => {
        let obj = { ...item };
        for (const key in item) {
          let format = this.columns.find((obj) => {
            return obj.value == key;
          })?.format;
          if (format) {
            let val = format(item[key]);
            obj[key] = typeof val == 'string' ? (val.includes('%') ? val?.split('%')?.[0] * 1 : !isNaN(val) ? val * 1 : val) : val;
          }
        }
        data.push(obj);
      });
      return data;
    },
    // 行样式
    elcellstyle ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex == 1) {
        if (row['cum_return'] >= 0) {
          return 'color: #E85D2D;';
        } else return 'color: #18C2A0;';
      }
    },
    fix2p (val) {
      return val == '--' ? val : !isNaN(val) ? Number(val * 100)?.toFixed(2) + '%' : '--';
    },
    fix3 (value) {
      return parseInt(value * 1000) / 1000 ? parseInt(value * 1000) / 1000 : '--';
    },
    // 导出excel
    exportExcel () {
      let list = [
        { label: '年份', value: 'year', fill: 'header' },
        { label: '年累计收益', value: 'cum_return', format: 'fix2p', fill: 'red_or_green' },
        { label: '排名', value: 'cum_return_rank', format: 'fix2p' },
        { label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
        { label: '排名', value: 'maxdrawdown_rank', format: 'fix2p' },
        { label: '波动率', value: 'volatility', format: 'fix2p' },
        { label: '排名', value: 'volatility_rank', format: 'fix2p' },
        { label: '夏普率', value: 'sharpe', format: 'fix2p' },
        { label: '排名', value: 'sharpe_rank', format: 'fix2p' }
      ];
      let indexList = [
        { label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
        { label: '排名', value: 'trackingerror_rank', format: 'fix2p' }
      ];
      let enhanceList = [
        { label: '信息比率', value: 'information', format: 'fix2p' },
        { label: '排名', value: 'information_rank', format: 'fix2p' }
      ];
      if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
        list.push(...indexList);
      } else if ((this.fundindextype = 'equityenhance')) {
        list.push(...enhanceList);
      }
      filter_json_to_excel(list, this.df_measure, '风险收益指标');
    },
    // 导出
    createPrintWord () {
      let list = [
        { label: '年份', value: 'year', fill: 'header' },
        { label: '年累计收益', value: 'cum_return', format: 'fix2p', fill: 'red_or_green' },
        { label: '排名', value: 'cum_return_rank', format: 'fix2p' },
        { label: '最大回撤', value: 'maxdrawdown', format: 'fix2p' },
        { label: '排名', value: 'maxdrawdown_rank', format: 'fix2p' },
        { label: '波动率', value: 'volatility', format: 'fix2p' },
        { label: '排名', value: 'volatility_rank', format: 'fix2p' },
        { label: '夏普率', value: 'sharpe', format: 'fix2p' },
        { label: '排名', value: 'sharpe_rank', format: 'fix2p' }
      ];
      let indexList = [
        { label: '跟踪误差', value: 'trackingerror', format: 'fix2p' },
        { label: '排名', value: 'trackingerror_rank', format: 'fix2p' }
      ];
      let enhanceList = [
        { label: '信息比率', value: 'information', format: 'fix2p' },
        { label: '排名', value: 'information_rank', format: 'fix2p' }
      ];
      if ((this.fundindextype = 'equityindex' || this.fundindextype == 'equityenhance')) {
        list.push(...indexList);
      } else if ((this.fundindextype = 'equityenhance')) {
        list.push(...enhanceList);
      }
      if (this.df_measure.length) {
        return [...exportTitle('风险收益指标'), ...exportTable(list, this.df_measure.reverse(), '', true)];
      } else {
        return [];
      }
    }
  }
};
</script>

<style></style>
