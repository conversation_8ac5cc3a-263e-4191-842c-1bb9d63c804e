<template>
	<div :id="'industryCapacity' + (showDescription ? '' : 'Description')">
		<analysis-card-title title="行业能力" :image_id="'industryCapacity' + (showDescription ? '' : 'Description')">
			<!-- <QuickTimePicker v-model="preset_time" @change="changeTimePicker"></QuickTimePicker> -->
		</analysis-card-title>
		<div class="charts_center_class" v-loading="loading" :id="'industryCapacityMain' + (showDescription ? '' : 'Description')">
			<v-chart
				:ref="'industryCapacity' + (showDescription ? '' : 'Description')"
				autoresize
				v-loading="loading"
				element-loading-text="暂无数据"
				element-loading-spinner="el-icon-document-delete"
				element-loading-background="rgba(239, 239, 239, 0.5)"
				style="width: 100%; height: 300px"
				:options="hangyeop"
			/>
		</div>
		<div v-if="showDescription">
			<analysis-description title="行业能力" :description="description"></analysis-description>
		</div>
		<!--<div class="flex_center" style="width: 100%" :id="'industryCapacityMain' + (showDescription ? '' : 'Description')">
			<div id="industryCapacity" v-loading="loading">
				<div class="charts_industry_class mr-40">
					<v-chart
						:ref="'industryCapacity' + (showDescription ? '' : 'Description')"
						autoresize
						v-loading="loading"
						element-loading-text="暂无数据"
						element-loading-spinner="el-icon-document-delete"
						element-loading-background="rgba(239, 239, 239, 0.5)"
						style="width: 100%; height: 300px"
						:options="hangyeop"
					/>
				</div>
				 <div v-show="classShow" class="industry_capacity">
					<div
						v-for="item in industryClassList"
						:key="item.value"
						@click="changeSwitch(item)"
						style="display: flex; align-items: center; cursor: pointer; margin: 6px 2px; width: 100px"
					>
						<div
							:style="
								item.show
									? `width: 16px;border-Radius:2px; height: 8px;background:${item.color}`
									: `width: 16px; height: 8px;border-Radius:2px;background:#cccccc`
							"
						></div>
						<div style="margin-left: 8px; font-size: 12px; color: #54657e">
							{{ item.label }}
						</div>
					</div>
				</div> 
			</div>
			<div style="width: 602px" v-if="showDescription">
				<analysis-description title="行业能力" :description="description"></analysis-description>
			</div>
		</div>-->
	</div>
</template>

<script>
import QuickTimePicker from '@/pages/tkdesign/marketAnalysis/component/QuickTimePicker.vue';
import analysisDescription from '@/components/components/components/analysisDescription/index.vue';
// 行业能力圈&&市场适应性
import { getIndustryRankInfo } from '@/api/pages/Analysis.js';

import { barChartOption } from '@/utils/chartStyle.js';

export default {
	name: 'industryCapacity',
	components: { QuickTimePicker, analysisDescription },
	props: {
		showDescription: {
			type: Boolean,
			default: false
		}
	},
	computed: {
		description() {
			return `综合考虑基金/基金经理超行业指数收益、行业配置次数、行业配置权重（等权），进 行同类型排名绘制行业能力圈图，越靠近外圈行业能力越强。`;
		}
	},
	data() {
		return {
			loading: true,
			show: true,
			preset_time: {
				radioValue: '1'
			},
			industryClassify: {
				// 板块分类-逆顺序
				finance: ['非银金融', '银行', '房地产'], // 金融地产-3
				cycle: ['基础化工', '公用事业', '交通运输', '石油石化', '建筑材料', '煤炭', '钢铁', '环保', '建筑装饰', '有色金属'], // 周期-10
				manufacture: ['国防军工', '汽车', '机械设备', '综合', '电力设备'], // 制造-5
				TMT: ['传媒', '通信', '计算机', '电子'], // TMT-4
				medicine: ['医药生物'], // 医药-1
				consume: ['农林牧渔', '家用电器', '商贸零售', '社会服务', '轻工制造', '纺织服饰', '食品饮料', '美容护理'] // 消费-8
			},
			classifyTranseName: {
				finance: '金融地产',
				cycle: '周期',
				manufacture: '制造',
				TMT: 'TMT',
				medicine: '医药',
				consume: '消费'
			},
			industryBigList: {
				金融地产: ['非银金融', '银行', '房地产'],
				周期: ['基础化工', '公用事业', '交通运输', '石油石化', '建筑材料', '煤炭', '钢铁', '环保', '建筑装饰', '有色金属'], // 周期-10
				制造: ['国防军工', '汽车', '机械设备', '综合', '电力设备'], // 制造-5
				TMT: ['传媒', '通信', '计算机', '电子'], // TMT-4
				医药: ['医药生物'], // 医药-1
				消费: ['农林牧渔', '家用电器', '商贸零售', '社会服务', '轻工制造', '纺织服饰', '食品饮料', '美容护理'] // 消费-8
			},
			colorList: {
				finance: '#B1B0F9',
				cycle: '#FFD600',
				manufacture: '#E8684A',
				TMT: '#7388A9',
				medicine: '#5AD8A6',
				consume: '#88C9E9'
			},
			hangyeop: {},
			info: {}
		};
	},
	methods: {
		openvideo() {
			window.open('https://www.bilibili.com/video/BV1N3411F7VT?share_source=copy_web');
		},
		// 监听时间选择框改变
		changeTimePicker(val) {
			if (val?.radioValue) {
				if (val.radioValue == 'custom') {
					this.info.start_date = val.startDate;
					this.info.end_date = val.endDate;
				} else {
					this.info.start_date = this.moment().subtract(val.radioValue, 'years').format('YYYY-MM-DD');
					this.info.end_date = this.moment().format('YYYY-MM-DD');
				}
			}
			this.getIndustryInfo();
		},
		// 获取数据
		async getData(info) {
			this.info = {
				...info,
				start_date: this.moment().subtract(this.preset_time.radioValue, 'years').format('YYYY-MM-DD'),
				end_date: this.moment().format('YYYY-MM-DD')
			};
			await this.getIndustryInfo();
		},
		// 获取行业能力圈
		async getIndustryInfo() {
			this.loading = true;
			let data = await getIndustryRankInfo({
				code: this.info.code,
				flag: this.info.flag,
				type: this.info.type,
				start_date: this.info.start_date,
				end_date: this.info.end_date,
				industry_section: this.info.type == 'equityhk' ? '恒生一级' : ''
			});
			this.loading = false;
			if (data?.mtycode == 200) {
				// this.getIndustryData(data?.data);
				this.updateChart(data?.data);
			}
		},
		// 图表样式修改 v2.0
		updateChart(data) {
			this.show = true;
			this.loading = false;
			let series = data.sort((a, b) => b.industryRank - a.industryRank);
			series = series.map((item) => {
				let industryClass = '';
				for (const key in this.industryBigList) {
					if (this.industryBigList[key].includes(item.industryName)) {
						industryClass = key;
					}
				}
				return {
					...item,
					industryClass
				};
			});
			let industrySection = Array.from(new Set(series.map((item) => item.industryClass)));
			this.hangyeop = barChartOption({
				toolbox: 'none',
				// color: ['#4096ff', '#FFD600', '#7388A9', '#E8684A', '#5AD8A6', '#88C9E9'],
				legend: {
					bottom: '0',
					data: industrySection
				},
				grid: {
					top: '12px',
					bottom: '38px'
				},
				tooltip: {
					// 坐标轴指示器，坐标轴触发有效
					type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
					formatter(params) {
						let index = params.findIndex((v) => v.value != '-');
						let value = `
                        <div style="font-weight:500;font-size:18pxmargin-bottom:8px">${params?.[index].seriesName}</div>
                        <div style="display:flex;justify-content:space-between;align-items:center;">
                            <div style="display:flex;align-items:center;">
                                <div style="margin-right:8px;border-radius:8px;width:8px;height:8px;background-color:${
																	params?.[index].color
																};"></div>
                                <div>${params[index].name}</div>
                                <div style="margin-left:4px">:</div>
                            </div>
                            <div>${(params[index]?.value * 100).toFixed(2)}%</div>
                        </div>`;
						return `<div style="width:240px;padding:12px;box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);border-radius:4px;background-color:#ffffff;color: rgba(0, 0, 0, 0.85);font-family: Helvetica Neue;font-size: 12px;font-style: normal;font-weight: 400;line-height: normal;">${value}</div>`;
					}
				},
				yAxis: [
					{
						type: 'value'
					}
				],
				xAxis: [
					{
						data: series.map((item) => {
							return item.industryName;
						}),
						isAlign: true
						// rotate: -45
					}
				],
				series: industrySection.map((item) => {
					return {
						name: item,
						stack: 'Total',
						type: 'bar',
						data: series.map((v) => (v.industryClass == item ? v.industryRank : '-'))
					};
				})
			});
		},
		// 获取数据
		getIndustryData(data) {
			this.show = true;
			this.type = this.info.type;
			this.loading = false;
			this.industryClassify = {
				// 板块分类-逆顺序
				finance: ['非银金融', '银行', '房地产', '金融业', '地产建筑业'], // 金融地产-3
				cycle: [
					'基础化工',
					'公用事业',
					'交通运输',
					'石油石化',
					'建筑材料',
					'煤炭',
					'钢铁',
					'环保',
					'建筑装饰',
					'有色金属',
					'能源业',
					'原材料业'
				], // 周期-10
				manufacture: ['国防军工', '汽车', '机械设备', '综合', '电力设备', '消费品制造业', '工业', '综合企业'], // 制造-5
				TMT: ['传媒', '通信', '计算机', '电子', '资讯科技业', '电讯业'], // TMT-4
				medicine: ['医药生物', '医疗保健业'], // 医药-1
				consume: [
					'农林牧渔',
					'家用电器',
					'商贸零售',
					'社会服务',
					'轻工制造',
					'纺织服饰',
					'食品饮料',
					'美容护理',
					'非必需性消费',
					'必需性消费',
					'消费者服务业'
				] // 消费-8
			};
			this.classifyTranseName = {
				finance: '金融地产',
				cycle: '周期',
				manufacture: '制造',
				TMT: 'TMT',
				medicine: '医药',
				consume: '消费'
			};
			this.industryClassList = [];
			for (const key in this.classifyTranseName) {
				this.industryClassList.push({
					label: this.classifyTranseName[key],
					value: key,
					show: true,
					color: this.colorList[key]
				});
			}
			this.industryData = { industry_rank: [], industry_name: [] };
			data.map((item) => {
				this.industryData.industry_rank.push(item.industryRank);
				this.industryData.industry_name.push(item.industryName);
			});
			this.formatIndustry();
		},
		// 格式化行业能力圈图
		formatIndustry() {
			if (this.industryData?.industry_rank && this.industryData?.industry_name) {
				let rank = [];
				let description = [];
				let obj = {
					rank: this.industryData?.industry_rank,
					description: this.industryData?.industry_name
				};
				obj.rank?.map((item, index) => {
					if (item * 1) {
						rank.push(item);
						description.push(obj.description[index]);
					}
				});
				let industryList = [];
				for (const key in this.industryClassify) {
					industryList.push(...this.industryClassify[key]);
				}
				let newRank = [];
				let newDes = [];
				description.map((item, index) => {
					if (industryList.indexOf(item) != -1) {
						newDes.push(item);
						newRank.push(rank[index]);
					}
				});
				this.hangyeop = this.drawChart(newRank, newDes);
			}
		},
		// 画图
		drawChart(industry, name) {
			let nameList = [];
			let dataList = [];
			let backgroundList = {};
			let backIndicator = Array.from({ length: name.length }, (item) => (item = { name: '', max: 1 }));
			for (let classify_name in this.industryClassify) {
				let theClassNum = 0;
				let classify = this.industryClassify[classify_name];
				backgroundList[classify_name] = [];
				classify.forEach((item) => {
					if (name.includes(item)) {
						theClassNum++;
						let index = name.indexOf(item);
						nameList.push({ name: item, max: 1 });
						dataList.push(industry[index]);
						backgroundList[classify_name].push(item);
					}
				});
				let zhName = this.classifyTranseName[classify_name];
				let zhIndex = Math.ceil(nameList.length - 1 - theClassNum / 2);
				// if (theClassNum > 0) {
				// 	backIndicator[zhIndex].name = zhName;
				// }
			}
			let option = {
				radar: [
					{
						show: true,
						// 底色
						startAngle: 90 + (360 / nameList.length) * 1,
						indicator: backIndicator,
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#A1A1A1',
								// 分隔线颜色
								width: 1,
								// 分隔线线宽
								type: [5]
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(255,255,255)']
								// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#A1A1A1',
								// 坐标轴线线的颜色。
								width: 1,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						center: ['center', 'center'],
						name: {
							textStyle: {
								fontSize: '12px',
								color: 'rgba(0,0,0,0.65)',
								fontWeight: 400
							}
						},
						nameGap: 6,
						radius: 130
					},
					{
						show: true,
						// 数据
						startAngle: 90 + 360 / nameList.length,
						splitLine: {
							// (这里是指所有圆环)坐标轴在 grid 区域中的分隔线。
							lineStyle: {
								color: '#A1A1A1',
								// 分隔线颜色
								width: 1,
								// 分隔线线宽
								type: [5]
							}
						},
						splitArea: {
							// 坐标轴在 grid 区域中的分隔区域，默认不显示。
							show: true,
							areaStyle: {
								// 分隔区域的样式设置。
								color: ['rgb(255,255,255)']
								// color: ['rgb(246,250,255)', 'rgb(246,250,255)']
								// 分隔区域颜色。分隔区域会按数组中颜色的顺序依次循环设置颜色。默认是一个深浅的间隔色。
							}
						},
						axisLine: {
							// (圆内的几条直线)坐标轴轴线相关设置
							lineStyle: {
								color: '#A1A1A1',
								// 坐标轴线线的颜色。
								width: 1,
								// 坐标轴线线宽。
								type: 'solid'
								// 坐标轴线线的类型。
							}
						},
						center: ['center', 'center'],
						name: {
							textStyle: {
								fontSize: '12px',
								color: 'rgba(0,0,0,0.65)',
								fontWeight: 400
							}
						},
						indicator: nameList,
						nameGap: 6,
						radius: 130
					}
				],
				tooltip: {
					trigger: 'axis',
					formatter(params) {
						return '行业：' + params[0].axisValue + ' ,' + params[0].data.toFixed(3);
					}
				},
				// toolbox: {
				// 	show: true,
				// 	feature: {
				// 		saveAsImage: { pixelRatio: 3 }
				// 	}
				// },
				series: [
					{
						// 数据
						itemStyle: {
							// 单个拐点标志的样式设置。
							normal: {
								color: '#ffffff',
								borderColor: '#4096ff',
								// 拐点的描边颜色。[ default: '#000' ]
								borderWidth: 2
								// 拐点的描边宽度，默认不描边。[ default: 0 ]
							}
						},
						lineStyle: {
							// 单项线条样式。
							normal: {
								color: '#4096ff',
								opacity: 1 // 图形透明度
							}
						},
						areaStyle: {
							// 单项区域填充样式
							normal: {
								color: '#4096ff', // 填充的颜色。[ default: "#000" ]
								opacity: 0.2
							}
						},
						name: '基金经理能力',
						type: 'radar',
						data: [
							{
								value: dataList,
								name: '基金经理能力'
							}
						],
						radarIndex: 1
					}
				]
			};
			let onlyNameList = nameList.map((item) => item.name);
			let seriesData = {
				// 底色
				type: 'radar',
				radarIndex: 0,
				data: []
			};
			for (let classify_name in this.industryClassify) {
				let classify = this.industryClassify[classify_name];
				let arr = Array.from({ length: dataList.length }, (item) => (item = ''));
				classify.forEach((item) => {
					if (onlyNameList.includes(item)) {
						let index = onlyNameList.indexOf(item);
						arr[index] = 1;
					}
				});
				// 需要每个色块往前加一位与前面的色块连上
				let preIndex = arr.indexOf(1);
				preIndex === 0 ? (arr[arr.length - 1] = 1) : (arr[preIndex - 1] = 1);

				seriesData.data.push({
					name: classify_name,
					symbol: 'none',
					value: arr,
					areaStyle: { color: this.colorList[classify_name], opacity: 0.2 },
					itemStyle: { color: this.colorList[classify_name] },
					lineStyle: { width: 0 }
				});
			}
			// if (nameList.length > 4) {
			this.classShow = true;
			option.series.unshift(seriesData);
			// } else {
			// 	this.classShow = false;
			// 	option = {
			// 		color: ['#4096FF'],
			// 		xAxis: {
			// 			margin: 12,
			// 			type: 'category',
			// 			data: name,
			// 			axisLabel: {
			// 				show: true,
			// 				color: 'rgba(0, 0, 0, 0.65)',
			// 				textStyle: {
			// 					fontSize: '12px'
			// 				}
			// 				// interval: 0
			// 			},
			// 			axisTick: {
			// 				show: false
			// 			},
			// 			axisLine: {
			// 				lineStyle: {
			// 					color: '#e9e9e9'
			// 				}
			// 			}
			// 		},
			// 		grid: {
			// 			left: '0',
			// 			right: '0',
			// 			bottom: '0',
			// 			top: '24px',
			// 			containLabel: true
			// 		},

			// 		yAxis: {
			// 			margin: 16,
			// 			axisLine: {
			// 				show: false
			// 			},
			// 			axisTick: {
			// 				show: false
			// 			},
			// 			axisLabel: {
			// 				textStyle: {
			// 					fontSize: '12px'
			// 				}
			// 			},
			// 			splitLine: {
			// 				show: true,
			// 				lineStyle: {
			// 					type: 'dashed'
			// 				}
			// 			},
			// 			type: 'value'
			// 		},
			// 		tooltip: {
			// 			textStyle: {
			// 				fontSize: '12px'
			// 			},
			// 			trigger: 'axis'
			// 		},
			// 		series: [
			// 			{
			// 				barWidth: '20px',
			// 				data: industry,
			// 				type: 'bar',
			// 				symbol: 'none'
			// 			}
			// 		]
			// 	};
			// }
			return option;
		},
		// 切换显示大类
		changeSwitch(item) {
			this.$set(
				this.industryClassList,
				this.industryClassList.findIndex((obj) => {
					return obj.value == item.value;
				}),
				{ ...item, show: !item.show }
			);
			this.industryClassify = {
				// 板块分类-逆顺序
				finance: ['非银金融', '银行', '房地产', '金融业', '地产建筑业'], // 金融地产-3
				cycle: [
					'基础化工',
					'公用事业',
					'交通运输',
					'石油石化',
					'建筑材料',
					'煤炭',
					'钢铁',
					'环保',
					'建筑装饰',
					'有色金属',
					'能源业',
					'原材料业'
				], // 周期-10
				manufacture: ['国防军工', '汽车', '机械设备', '综合', '电力设备', '消费品制造业', '工业', '综合企业'], // 制造-5
				TMT: ['传媒', '通信', '计算机', '电子', '资讯科技业', '电讯业'], // TMT-4
				medicine: ['医药生物', '医疗保健业'], // 医药-1
				consume: [
					'农林牧渔',
					'家用电器',
					'商贸零售',
					'社会服务',
					'轻工制造',
					'纺织服饰',
					'食品饮料',
					'美容护理',
					'非必需性消费',
					'必需性消费',
					'消费者服务业'
				] // 消费-8
			};
			this.classifyTranseName = {
				finance: '金融地产',
				cycle: '周期',
				manufacture: '制造',
				TMT: 'TMT',
				medicine: '医药',
				consume: '消费'
			};
			let classifyTranseName = {};
			let industryClassify = {};
			this.industryClassList.map((item) => {
				if (item.show) {
					classifyTranseName[item.value] = item.label;
					industryClassify[item.value] = this.industryClassify[item.value];
				}
			});
			this.classifyTranseName = classifyTranseName;
			this.industryClassify = industryClassify;
			this.formatIndustry();
		},
		async createPrintWord(info) {
			await this.getData(info);
			return await new Promise((resolve, reject) => {
				this.$nextTick(async () => {
					let key = 'industryCapacityMain' + (this.showDescription ? '' : 'Description');
					let height = document.getElementById(key).clientHeight;
					let width = document.getElementById(key).clientWidth;
					let canvas = await this.html2canvas(document.getElementById(key), {
						scale: 3
					});
					resolve([
						...this.$exportWord.exportTitle('行业能力圈'),
						...this.$exportWord.exportChart(canvas.toDataURL('image/jpg'), {
							width,
							height
						})
					]);
				});
			});
		}
	}
};
</script>

<style scoped>
.charts_two_class {
	height: 260px;
}
</style>
