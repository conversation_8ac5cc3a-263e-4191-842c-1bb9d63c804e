<!--  -->
<template>
	<div>
		<div
			v-show="haveName != ''"
			style="
				font-weight: 400;
				font-size: 14px;
				line-height: 22px;
				color: rgba(0, 0, 0, 0.85);
				margin-left: 0px;
				margin-right: 16px;
				margin-bottom: 4px;
			"
		>
			{{ haveName }}{{ address }}
		</div>
		<div class="boxOnlyYSF">
			<operator v-if="is_range" ref="operator" @resolveMathRange="resolveMathRange"></operator>
			<div>
				<el-cascader @change="change" placeholder="请选择时间范围" style="width: 100px" v-model="date" :options="option"> </el-cascader>
			</div>
			<div v-if="haveBenchmarkList.includes(haveName)" style="margin-left: 16px">
				<el-select v-model="index" placeholder="选择参照的benchamrk" @change="resolveFather">
					<el-option v-for="item in indexList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
				</el-select>
			</div>
			<el-dropdown style="margin-left: 16px" @command="commandValueType">
				<el-button type="primary">
					<span>{{ valueType == 'value' ? '值' : '分位' }}</span>
					<i class="el-icon-arrow-down el-icon--right"></i>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<el-dropdown-item command="rank">分位</el-dropdown-item>
					<el-dropdown-item command="value">值</el-dropdown-item>
				</el-dropdown-menu>
			</el-dropdown>
			<el-dropdown style="margin-left: 16px" @command="command">
				<el-button type="primary">
					<div class="flex_start">
						<div v-if="valueType == 'rank'">
							<span v-show="haveName !== '平均恢复周期' && haveName !== '平均下行周期'">{{
								iconFlag !== '' ? (iconFlag == '&lt;=' ? '坏于' : '好于') : '运算符'
							}}</span>
							<span v-show="haveName == '平均恢复周期' || haveName == '平均下行周期'">{{
								iconFlag !== '' ? (iconFlag == '&lt;=' ? '长于' : '短于') : '运算符'
							}}</span>
						</div>
						<div v-else>
							{{ iconFlag != '' ? (iconFlag == 'all' ? '所有' : iconFlag) : '运算符' }}
						</div>
						<i class="el-icon-arrow-down el-icon--right"></i>
					</div>
				</el-button>
				<el-dropdown-menu slot="dropdown">
					<template v-if="valueType == 'rank'">
						<el-dropdown-item v-show="haveName == '平均恢复周期' || haveName == '平均下行周期'" command=">=">短于</el-dropdown-item>
						<el-dropdown-item v-show="haveName == '平均恢复周期' || haveName == '平均下行周期'" command="<=">长于</el-dropdown-item>
						<el-dropdown-item v-show="haveName !== '平均恢复周期' && haveName !== '平均下行周期'" command=">=">好于</el-dropdown-item>
						<el-dropdown-item v-show="haveName !== '平均恢复周期' && haveName !== '平均下行周期'" command="<=">坏于</el-dropdown-item>
					</template>
					<template v-else>
						<el-dropdown-item command="<=">&lt;=</el-dropdown-item>
						<el-dropdown-item command=">=">&gt;=</el-dropdown-item>
					</template>
					<!-- <el-dropdown-item command="all">所有</el-dropdown-item> -->
					<!-- <el-dropdown-item command="<">&lt;</el-dropdown-item>
					<el-dropdown-item command=">">&gt;</el-dropdown-item> -->
				</el-dropdown-menu>
			</el-dropdown>

			<div style="margin-left: 0px; display: flex; align-items: center">
				<!-- <div style="padding:5px;background:#ecf5ff;border:1px #f8f8f8;">
            {{iconFlag=='all'?'所有':iconFlag}}
        </div> -->
				<div style="margin-left: 16px">
					<el-input type="number" @input="inputChange" :placeholder="valueType == 'value' ? haveName : '百分之多少的同类'" v-model="input">
						<template slot="append" v-if="valueType == 'value' && unit">{{ unit }}</template>
						<template slot="append" v-else-if="valueType == 'rank'">%</template>
					</el-input>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import operator from '@/pages/filter/fund/beta/componentsFilter/components/operator.vue';
export default {
	props: {
		is_range: {
			type: Boolean,
			default: false
		},
		haveName: {
			type: String,
			default: ''
		},
		dataX: {
			type: Object,
			default: {}
		},
		placeholder: {
			type: String
		},
		indexFlag: {
			type: Number
		},
		baseIndexFlag: {
			type: Number
		},
		optionsselect: {
			type: Array,
			default: []
		},
		indexList: {
			type: Array,
			default: []
		},
		valueTypes: {
			type: Object,
			default: null
		}
	},
	//import引入的组件需要注入到对象中才能使用
	components: { operator },
	data() {
		//这里存放数据
		return {
			iconFlag: '',
			valueType: 'value',
			showBox: false,
			input: '',
			date: '',
			haveBenchmarkList: [
				'最大回撤比',
				'波动率比',
				'信息比率',
				'上攻潜力(周)',
				'月胜率',
				'詹森系数',
				'特诺系数',
				'上行捕获',
				'下行捕获',
				'择时gamma',
				'M2'
			],
			option: [
				{
					value: 'recent',
					label: '近期表现',
					children: [
						{ value: '1w', label: '一周' },
						{ value: '2w', label: '两周' },
						{ value: '1m', label: '一月' },
						{ value: '2m', label: '两月' },
						{ value: '1q', label: '一季' },
						{ value: '2q', label: '两季' },
						{ value: '1y', label: '一年' },
						{ value: '2y', label: '两年' },
						{ value: '3y', label: '三年' },
						{ value: '5y', label: '五年' }
					]
				},
				{
					value: 'from',
					label: '从那时起',
					children: [
						
						{
							"label": "新能源顶点",
							"value": "2021-09-14"
						},
						{
							"label": "抱团破灭",
							"value": "2021-02-21"
						},
						{
							"label": "811汇改",
							"value": "2015-08-26"
						},
						{
							"label": "中美贸易战",
							"value": "2018-03-22"
						},
					

					]
				}
			],
			index: '',

			mathRange: { mathRange: 'avg' }
		};
	},
	//监听属性 类似于data概念
	computed: {
		address() {
			if (this.FUNC.isEmpty(this.optionsselect)) {
				this.option = this.optionsselect;
			}
		},
		unit() {
			// 百分化的值
			let rank_list = [
				'波动率',
				'最大回撤',
				'在险价值',
				'期望损失',
				'下行风险',
				'年化收益率',
				'累计收益率',
				'月胜率',
				'特诺系数',
				'上行捕获',
				'下行捕获',
				'M2'
			];
			let day_list = ['平均下行周期', '平均恢复周期'];
			let value_list = [
				'最大回撤比',
				'波动率比',
				'痛苦指数',
				'夏普率（rf==0）',
				'夏普率（rf==4%）',
				'夏普率（动态rf）',
				'卡码率',
				'索提诺系数（rf==0）',
				'索提诺系数（rf==4%）',
				'索提诺系数（动态rf）',
				'稳定系数',
				'凯莉系数',
				'信息比率',
				'上攻潜力（周）',
				'詹森系数',
				'择时gamma'
			];
			if (rank_list.includes(this.haveName)) {
				return '%';
			} else if (day_list.includes(this.haveName)) {
				return '天';
			}
		}
	},
	//监控data中的数据变化
	watch: {
		valueTypes(val) {
			this.valueType = val?.dataResult?.[0]?.valueType || 'value';
		},
		dataX(val) {
			if (val.dataResult && val.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = val.dataResult[0].flag;
				this.input = val.dataResult[0].value;
				this.date = val.dataResult[0].date;
				this.index = val.dataResult[0].benchmark;
				this.option = val.dataResult[0].option;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(val.dataResult[0].mathRange);
				}

				// this.getDate();
			}
		}
	},
	//方法集合
	methods: {
		change() {
			this.resolveFather();
		},
		command(e) {
			this.iconFlag = e;
			this.showBox = true;
			this.resolveFather();
		},
		commandValueType(e) {
			this.valueType = e;
			this.showBox = true;
			this.resolveFather();
		},
		inputChange() {
			this.resolveFather();
		},
		resolveMathRange(obj) {
			this.mathRange = obj;
			this.resolveFather();
		},
		resolveFather() {
			this.$emit(
				'boxOnlyYSFNameChange',
				this.baseIndexFlag,
				this.indexFlag,
				this.input,
				this.iconFlag,
				this.date,
				this.option,
				this.FUNC.isEmpty(this.date) && this.FUNC.isEmpty(this.input) && this.FUNC.isEmpty(this.iconFlag),
				this.mathRange,
				this.valueType,
				this.index,
				this.indexList.find((v) => v.value == this.index)?.label
			);
		}
	},
	mounted() {
		// this.getDate();
		if (JSON.stringify(this.dataX) != '{}') {
			if (this.dataX.dataResult && this.dataX.dataResult.length > 0) {
				this.showBox = true;
				this.iconFlag = this.dataX.dataResult[0].flag;
				this.input = this.dataX.dataResult[0].value;
				this.date = this.dataX.dataResult[0].date;
				this.index = this.dataX.dataResult[0].benchmark;
				this.option = this.dataX.dataResult[0].option;
				if (this.$refs['operator']) {
					this.$refs['operator'].getFlag(this.dataX.dataResult[0].mathRange);
				}
			}
		}
	}
};
</script>
<style lang="scss" scoped>
//@import url(); 引入公共css类
.boxOnlyYSF {
	display: flex;
	align-items: center;
}
</style>
