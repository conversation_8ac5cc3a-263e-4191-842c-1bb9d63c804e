<template>
	<div class="basic_info_fund px-12 py-12 mt-12">
		<div :class="index == 0 ? 'flex_start' : 'flex_start mt-8'" v-for="(item, index) in column" :key="index">
			<div class="flex_start mr-20" :style="item.length == 1 ? 'flex:1' : 'min-width: 180px'" v-for="obj in item" :key="obj.value">
				<div
					class="mr-8 basic_info_fund_laabel"
					:style="obj.method ? 'color:#4096ff;cursor: pointer;' : ''"
					@click="obj.method ? obj.method(obj.label) : ''"
				>
					{{ obj.label }}
				</div>
				<div>{{ data[obj.value] }}</div>
			</div>
		</div>
	</div>
</template>

<script>
import { getBasicInfo } from '@/api/pages/Analysis.js';
export default {
	data() {
		return {
			column: [],
			data: {
				fundcompany: '',
				currentsize: '',
				found: '',
				founddate: '',
				manager: '',
				applying_redeem: '',
				csrctype: '',
				benchmark: '',
				managed: 0
			},
			info: {}
		};
	},
	methods: {
		getData(info) {
			let is_manager = this.$route.path?.includes('fundmanagerdetail');
			this.info = info;
			if (this.info.flag == 2) {
				this.column = is_manager?[
					[
						{ label: '基金公司', value: 'glr', format: this.fix_company },
						{ label: '在管基金规模', value: 'netasset', format: this.fix_size },
						
					],
					[
						// { label: '基金类型', value: 'csrctype' },
						{ label: '职业开始时间', value: 'managedFrom' },
						{ label: '历任基金', value: 'managed_num' }
					]
					// [{ label: '业绩基准', value: 'benchmark' }]
				]:[
					[
						{ label: '基金公司', value: 'glr', format: this.fix_company },
						{ label: '在管基金规模', value: 'netasset', format: this.fix_size },
						{
							label: '在管基金机构占比',
							value: 'found',
							format: this.fix_b,
							method: this.goDetail
						}
					],
					[
						// { label: '基金类型', value: 'csrctype' },
						{ label: '职业开始时间', value: 'managedFrom' },
						{ label: '历任基金', value: 'managed_num' }
					]
					// [{ label: '业绩基准', value: 'benchmark' }]
				];
			} else {
				this.column = [
					[
						{
							label: '基金公司',
							value: 'fundcompany',
							format: this.fix_company
						},
						{
							label: '基金规模',
							value: 'currentsize',
							format: this.fix_size,
							method: this.goDetail
						},
						{
							label: '机构占比',
							value: 'institutionratio',
							format: this.fix_b,
							method: this.goDetail
						}
					],
					[
						{ label: '成立时间', value: 'founddate' },
						{ label: '基金经理', value: 'manager_name' },
						{ label: '申购状态', value: 'applying_redeem' }
					],
					[
						{ label: '基金类型', value: 'csrctype' },
						{ label: '二级分类', value: 'windtype' }
					],
					[{ label: '业绩基准', value: 'benchmark' }]
				];
			}
			this.getBasicInfo();
		},
		// 调用接口，获取基金/基金经理基础信息
		async getBasicInfo() {
			let data = await getBasicInfo({
				code: this.info.code,
				type: this.info.type,
				flag: this.info.flag
			});
			if (data?.mtycode == 200) {
				let result = { ...data?.data };
				// 将二维数组转化为一维数组
				let column = [];
				this.column.map((item) => {
					column.push(...item);
				});
				// 遍历一维数组，格式化数据
				column.map((item) => {
					for (const key in data?.data) {
						if (item.value == key) {
							result[key] = item.format ? item.format(data?.data[key]) : data?.data[key];
						}
					}
				});
				this.data = {
					...result,
					applying_redeem: data?.data.applyingtype + ',' + data?.data.redeemtype
				};
			}
		},
		// 格式化基金公司名称
		fix_company(val) {
			return val
				.replace('基金管理有限公司', '基金')
				.replace('基金管理有限责任公司', '基金')
				.replace('基金管理股份有限公司', '基金')
				.replace('基金管理有限公司', '基金')
				.replace('有限公司', '')
				.replace('有限责任公司', '')
				.replace('资产管理', '资管');
		},
		// 取两位小数添加百分号
		fix_b(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '%' : '--';
		},
		// 添加亿
		fix_size(val) {
			return val * 1 && !isNaN(val) ? (val * 1).toFixed(2) + '亿' : '--';
		},
		// 前往详情分析
		goDetail(name) {
			if (name) {
				this.$event.$emit('position-analysis', name);
			}
		},
		createPrintWord() {
			let list = [];
			console.log(this.column);
			this.column.map((item) => {
				item.map((obj) => {
					list.push({ label: obj.label, value: obj.value });
				});
			});
			return [...this.$exportWord.exportDoubleColumnTable(list, this.data)];
		}
	}
};
</script>
<style lang="scss" scoped>
.basic_info_fund {
	border-radius: 4px 4px 0 0;
	background: #ecf5ff;
	border-bottom: 1px dashed #d9d9d9;
	font-size: 14px;
	color: rgba(0, 0, 0, 0.85);
	font-family: PingFang SC;
	font-size: 14px;
	font-style: normal;
	font-weight: 400;
	.basic_info_fund_laabel {
		color: rgba(0, 0, 0, 0.45);
		font-family: PingFang SC;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
	}
}
</style>
