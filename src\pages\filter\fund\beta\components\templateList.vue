<template>
	<div
		style="width: 210px;  border-radius: 0px 0px 0px 4px;"
	>
		<!-- <div style="height: 48px">
			<div style="height: 40px; margin-top: 8px; background: #4096ff; display: flex; justify-content: space-between; align-items: center">
				<div style="margin-left: 16px; font-weight: 500; font-size: 14px; color: white">模板</div>
				<div style="margin-right: 16px">
					<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M3.02638 10.7485C3.05763 10.7485 3.08888 10.7454 3.12013 10.7407L5.74825 10.2798C5.7795 10.2735 5.80919 10.2595 5.83107 10.236L12.4545 3.6126C12.469 3.59814 12.4805 3.58097 12.4883 3.56207C12.4962 3.54317 12.5002 3.52291 12.5002 3.50244C12.5002 3.48198 12.4962 3.46171 12.4883 3.44281C12.4805 3.42391 12.469 3.40674 12.4545 3.39229L9.85763 0.793848C9.82794 0.76416 9.78888 0.748535 9.74669 0.748535C9.7045 0.748535 9.66544 0.76416 9.63576 0.793848L3.01232 7.41729C2.98888 7.44072 2.97482 7.46885 2.96857 7.5001L2.50763 10.1282C2.49243 10.2119 2.49786 10.2981 2.52345 10.3792C2.54905 10.4603 2.59403 10.534 2.6545 10.5938C2.75763 10.6938 2.88732 10.7485 3.02638 10.7485ZM4.0795 8.02354L9.74669 2.35791L10.892 3.50322L5.22482 9.16885L3.83575 9.41416L4.0795 8.02354ZM12.7498 12.061H1.24982C0.973254 12.061 0.749817 12.2845 0.749817 12.561V13.1235C0.749817 13.1923 0.806067 13.2485 0.874817 13.2485H13.1248C13.1936 13.2485 13.2498 13.1923 13.2498 13.1235V12.561C13.2498 12.2845 13.0264 12.061 12.7498 12.061Z"
							fill="white"
						/>
					</svg>
				</div>
			</div>
		</div> -->
		<div v-for="(item, index) in concaArr(gridData, listar)" :key="index">
			
			<div :class="nowModel == item.model_name ? 'model_active' : ''" @click="chooseModel(item)">
				<div style="display: flex; align-items: center; justify-content: space-between" class="boxModel">
					<div style="padding-left: 16px" class="normalFontQ">
						<span>{{ item.model_name }}</span
						><span v-if="item.types && item.types == 'self'"></span><span v-else>(慧捕基模板)</span>
					</div>
					<!-- <div
						@click="editModel(item)"
						style="padding-right: 16px"
						v-show="nowModel == item.model_name && showjslogo == true && item.ispublic == 'False'"
					>
						<el-button type="text">公开</el-button>
					</div>
					<div
						@click="editModel(item)"
						v-show="nowModel == item.model_name && showjslogo == true && item.ispublic == 'True'"
					>
						<el-button type="text">隐藏</el-button>
					</div> -->
					<!-- 隐藏修改按钮 -->
					<!-- <div v-show="nowModel == item.model_name" style="padding-right: 12px; cursor: pointer">
						<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
							<g clip-path="url(#clip0_7809_187807)">
								<path
									d="M3.02638 10.7485C3.05763 10.7485 3.08888 10.7454 3.12013 10.7407L5.74825 10.2798C5.7795 10.2735 5.80919 10.2595 5.83107 10.236L12.4545 3.6126C12.469 3.59814 12.4805 3.58097 12.4883 3.56207C12.4962 3.54317 12.5002 3.52291 12.5002 3.50244C12.5002 3.48198 12.4962 3.46171 12.4883 3.44281C12.4805 3.42391 12.469 3.40674 12.4545 3.39229L9.85763 0.793848C9.82794 0.76416 9.78888 0.748535 9.74669 0.748535C9.7045 0.748535 9.66544 0.76416 9.63576 0.793848L3.01232 7.41729C2.98888 7.44072 2.97482 7.46885 2.96857 7.5001L2.50763 10.1282C2.49243 10.2119 2.49786 10.2981 2.52345 10.3792C2.54905 10.4603 2.59403 10.534 2.6545 10.5938C2.75763 10.6938 2.88732 10.7485 3.02638 10.7485ZM4.0795 8.02354L9.74669 2.35791L10.892 3.50322L5.22482 9.16885L3.83575 9.41416L4.0795 8.02354ZM12.7498 12.061H1.24982C0.973254 12.061 0.749817 12.2845 0.749817 12.561V13.1235C0.749817 13.1923 0.806067 13.2485 0.874817 13.2485H13.1248C13.1936 13.2485 13.2498 13.1923 13.2498 13.1235V12.561C13.2498 12.2845 13.0264 12.061 12.7498 12.061Z"
									fill="#4096ff"
								/>
							</g>
							<defs>
								<clipPath id="clip0_7809_187807">
									<rect width="14" height="14" fill="white" />
								</clipPath>
							</defs>
						</svg>
					</div> -->
				</div>
			</div>
		</div>
		<div style="padding: 10px 16px; border-top: 1px solid #e9e9e9;">
			<div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
				<el-button type="text" @click="saveModel" style="color: #4096ff; padding: 0;">保存</el-button>
				<el-button
					type="text"
					@click="delModel"
					:style="nowModel == '' ? '' : 'color: #4096ff; padding: 0;'"
					:disabled="nowModel == '' ? true : false"
				>删除</el-button>
			</div>
			<div>
				<!-- <el-link @click="addModel"> <i class="el-icon-plus"></i> 新建</el-link> -->
			</div>
		</div>
		<el-dialog title="保存模板" :visible.sync="showModel" width="30%">
			<div style="width: 100%; display: flex; align-items: center; margin-left: 10px">
				<div style="margin-right: 10px">名称</div>
				<div><el-input v-model="input" placeholder="请输入模板名称"></el-input></div>
			</div>
			<div v-if="showjslogo" style="margin-top: 10px; margin-left: 10px">
				<el-checkbox v-model="checked">作为<span style="color: red; font-weight: 600">慧捕基模板</span></el-checkbox>
			</div>
			<div style="text-align: right; margin-right: 10px; margin-top: 10px">
				<el-button type="primary" @click="submitModel">确认</el-button>
			</div>
		</el-dialog>
	</div>
</template>

<script>
import { getModelList, saveModel, editModel, deleteModel } from '@/api/pages/Tools.js';

export default {
	data() {
		return {
			gridData: [],
			listar: [],
			showModel: false,
			input: '',
			checked: false,
			showjslogo: false
		};
	},
	props: {
		nowModel: {
			type: String
		},
		nowModelId:{
			type: Number
		}
	},
	mounted() {
		this.getmodals();
		if (this.$store.state.userType.indexOf('staff') >= 0 || this.$store.state.userType.indexOf('super') >= 0) {
			this.showjslogo = true;
		} else {
			this.showjslogo = false;
		}
	},
	methods: {
		addModel() {
			this.$emit('chooseModel', '');
		},
		saveModel() {
			this.$emit('saveModel');
		},
		chooseModel(val) {
			this.$emit('chooseModel', val);
		},
		async editModel(item) {
			await editModel({ id: item.model_id, ispublic: item.ispublic == 'False' ? true : false, type: 'equity' }).catch(() => {});
			this.$message.success('成功');
			// 公开模板
			this.getmodals();
		},
		async getmodals() {
			//获取模板
			let res = await getModelList({ type: 'equity', ismanager: 'false', flag: 'filter' }).catch((err) => {
				this.gridData = [];
				this.listar = [];
			});
      let data = res.data;
			this.gridData =
				data?.self_data?.map((item) => {
					return {
						ispublic: item.ispublic,
						modelid: item.model_id,
						model_args: item.model_args,
						model_description: item.model_description,
						model_name: item.model_name,
						model_time: item.model_time,
						user_id: item.user_id,
						types: 'self'
					};
				}) || [];
			this.listar = data.owl_data || [];
	  		console.log('ssssssssssssssssssssss',this.gridData)
			
		},
		async submitModel() {
			if (this.input == null || this.input == '') {
				that.$message('请输入模板名称');
			} else {
				await saveModel({
					type: 'equity',
					ismanager: 'false',
					model_name: this.input,
					model_description: '',
					ispublic: this.checked,
					model_args: {
						// value: this.listSelect,
						// isSame: this.isSame,
						// radioType: this.radioType,
						// radioInput: this.radioInput
					},
					flag: 'filter'
				}).catch((err) => {
					this.$message.error('模板保存失败');
				});
				this.$message.success('保存模板成功');
				this.getmodals();
				this.showModel = false;
			}
		},
		// 切换模板
		changeMerge(e) {
			this.isMerge = e;
		},
		async delModel() {
			let type =
				this.gridData.concat(this.listar)[this.gridData.concat(this.listar).findIndex((item) => item.model_name == this.nowModel)]?.types ||
				'mty';
			if (type == 'mty') {
				this.$message.error('无权限删除');
			} else {
				await deleteModel({ type: 'equity', ismanager: 'false', model_name: this.nowModel, model_id: this.nowModelId, flag: 'filter' });
				this.modelid = '';
				this.getmodals();
				this.$message.success('删除成功');
			}
		},
		// 连接两个数据
		concaArr(arr1, arr2) {
			let arr = [];
			arr1.concat(arr2).map((item) => {
				let index = arr.findIndex((obj) => {
					return obj.modelid == item.modelid;
				});
				if (index == -1) {
					arr.push(item);
				}
			});
			return arr;
		}
	}
};
</script>
<style lang="scss" scoped>
.boxModel {
	height: 40px;
	cursor: pointer;
}
.model_active {
	background: rgba(255, 145, 3, 0.1);
	/* 主色调 */
	span {
		color: #4096ff;
	}

	border-right: 2px solid #4096ff;
}
</style>
